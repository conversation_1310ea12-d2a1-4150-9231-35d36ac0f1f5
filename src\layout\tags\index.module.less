.layout_tags {
  z-index: 299;
  display: flex;
  justify-content: space-between;
  height: 32px;
  padding: 4px 12px;
  line-height: 32px;
  background: #fff;
  // border-top: dashed 1px #d9d9d9;
  box-sizing: border-box;

  &__main {
    position: relative;
    width: calc(100% - 116px);
    height: 24px;
    overflow: hidden;

    &-cont {
      position: absolute;
      height: 100%;
      padding: 0 2px;
      overflow: visible;
      white-space: nowrap;
      transition: left .5s ease;
    }
  }

  &__btn-space {
    margin-left: 4px;
  }
}
