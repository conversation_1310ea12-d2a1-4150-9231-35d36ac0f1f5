import { lazy } from '@loadable/component'
import type { RouteObject } from '../types'
import { LayoutGuard } from '../guard'
import { LazyLoad } from '@/components/LazyLoad'

const DirectoryRoute: RouteObject = {
  path: '/directory',
  name: 'Directory',
  element: <LayoutGuard />,
  meta: {
    title: '目录管理',
    icon: 'folder',
    orderNo: 2,
    hideChildrenInMenu: true
  },
  children: [
    {
      path: '',
      name: 'DirectoryPage',
      element: LazyLoad(lazy(() => import('@/views/directory'))),
      meta: {
        title: '目录管理',
        key: 'directory',
        icon: 'folder',
        orderNo: 2,
        hideMenu: true
      }
    }
  ]
}

export default DirectoryRoute
