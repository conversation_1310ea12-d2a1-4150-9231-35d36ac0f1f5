import { type FC, useEffect, useRef } from 'react'
import * as echarts from 'echarts/core'
import { GridComponent, TooltipComponent, LegendComponent } from 'echarts/components'
import { LineChart as EchartsLineChart } from 'echarts/charts'
import { UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必要的组件
echarts.use([GridComponent, TooltipComponent, LegendComponent, EchartsLineChart, UniversalTransition, CanvasRenderer])

interface ResourceTrendChartProps {
  loading: boolean
  height?: number
  width?: number
}

const ResourceTrendChart: FC<ResourceTrendChartProps> = ({ loading, height = 200, width = 500 }) => {
  const chartRef = useRef<HTMLDivElement>(null)
  let chartInstance: echarts.ECharts | null = null

  const getChartOptions = () => {
    // 生成最近七天的日期
    const generateLastSevenDays = () => {
      const dates = []
      for (let i = 6; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        const month = (date.getMonth() + 1).toString().padStart(2, '0')
        const day = date.getDate().toString().padStart(2, '0')
        dates.push(`${month}-${day}`)
      }
      return dates
    }

    return {
      // title: {
      //   text: '数据资源趋势',
      //   textStyle: {
      //     color: '#ffffff',
      //     textShadow: '0px 0px 19px #009bff',
      //     fontSize: 16
      //   },
      //   left: 'left'
      // },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['数据库', '文件', 'API'],
        textStyle: {
          color: '#fff'
        },
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: 70,
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: generateLastSevenDays(),
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        axisLabel: {
          color: '#fff'
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.3)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        },
        axisLabel: {
          color: '#fff'
        }
      },
      series: [
        {
          name: '数据库',
          type: 'line',
          stack: 'Total',
          data: [3, 5, 3, 6, 3, 5, 3],
          lineStyle: {
            color: '#18FEFE'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(24, 254, 254, 0.3)' },
              { offset: 1, color: 'rgba(24, 254, 254, 0)' }
            ])
          }
        },
        {
          name: '文件',
          type: 'line',
          stack: 'Total',
          data: [2, 3, 1, 2, 1, 3, 2],
          lineStyle: {
            color: '#15DA7A'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(21, 218, 122, 0.3)' },
              { offset: 1, color: 'rgba(21, 218, 122, 0)' }
            ])
          }
        },
        {
          name: 'API',
          type: 'line',
          stack: 'Total',
          data: [3, 4, 2, 4, 7, 5, 3],
          lineStyle: {
            color: '#fd9e13'
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(253, 158, 19, 0.3)' },
              { offset: 1, color: 'rgba(253, 158, 19, 0)' }
            ])
          }
        }
      ]
    }
  }

  const initChart = () => {
    const el = chartRef.current
    if (!el) return

    chartInstance = echarts.init(el)
    chartInstance.setOption(getChartOptions())
  }

  const handleResize = () => {
    chartInstance?.resize()
  }

  useEffect(() => {
    initChart()
    window.addEventListener('resize', handleResize)

    return () => {
      chartInstance?.dispose()
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  useEffect(() => {
    if (!loading && chartInstance) {
      chartInstance.setOption(getChartOptions())
    }
  }, [loading])

  return <div ref={chartRef} style={{ width: '100%', height: `${height}px` }} />
}

export default ResourceTrendChart
