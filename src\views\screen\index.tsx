import styles from './index.module.less'
import { useState, useEffect, useRef, useMemo, useCallback } from 'react'
import React from 'react'
import { ClockCircleOutlined, CalendarOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import Pie3DChart from '../home/<USER>/Pie3DChart'
import HazardInspectionCard from './components/HazardInspectionCard'
import LineChart from './components/LineChart'
import DataTable from './components/DataTable'
import * as echarts from 'echarts'
import 'echarts-wordcloud'
import rightBom from '../../assets/images/rightBom.jpg'
import dataRegistrationCertificate from '../../assets/images/dataRegistrationCertificate.png'
import dataProductCertificate from '../../assets/images/dataProductCertificate.png'
import hetong from '../../assets/images/hetong.jpg'
import top from '../../assets/images/top.png'
import bom from '../../assets/images/bom.png'
import rzcs from '../../assets/images/rzcs.png'
import sjzy from '../../assets/images/sjzy.png'
import sjcpl from '../../assets/images/sjcpl.png'
import {
  Modal,
  Select,
  Button,
  Table,
  Pagination,
  Form,
  Input,
  Checkbox,
  Flex,
  DatePicker,
  Steps,
  Tooltip,
  message,
  Cascader
} from 'antd'
import { BorderBox8 } from '@jiaminghi/data-view-react'
import RiskTypeChart from './components/RiskTypeChart'
import ResourceTrendChart from './components/ResourceTrendChart'
import { useScreenData } from '@/hooks/useScreenData'
import PerformanceMonitor, { useRenderPerformanceMonitor } from '@/components/PerformanceMonitor'
// 注释掉所有接口请求，使用本地JSON数据
// import {
//   getCount,
//   dataTrendChart,
//   dataListedCount,
//   dataContractListedSignCount,
//   dataExList,
//   dataCalculatedCount,
//   getDataList,
//   listTreeCategory,
//   identity,
//   screenDataToken,
//   screenDataDirectory,
//   screenDataSigning,
//   screenTask,
//   screenDeliveryRecord,
//   screenOrder,
//   screenTaskContent,
//   measurementBilling,
//   calculateDataPrice,
//   listedVoucher,
//   registerVoucher
// } from '@/api/screen'

// 导入本地JSON数据
import screenDataJson from '@/data/screenData.json'
// 为window对象添加APP_CONFIG属性的类型声明
declare global {
  interface Window {
    APP_CONFIG: any
  }
}
const Screen = () => {
  const { RangePicker } = DatePicker

  // 性能监控
  useRenderPerformanceMonitor('Screen')

  // 使用优化的数据管理hook
  const { screenData, loading: dataLoading, loadInitialData, refreshAllData } = useScreenData()
  const {
    counts,
    xData,
    yData,
    isDataLoaded,
    listedCount,
    listedCompareRate,
    signCount,
    signCompareRate,
    tradedCount,
    pendingCount
  } = screenData

  const [time, setTime] = useState('')
  const [date, setDate] = useState('')
  const [loading, setLoading] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const scrollIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [selectedRowData, setSelectedRowData] = useState<any>(null)
  const [identityModalVisible, setIdentityModalVisible] = useState(false)
  const [itemValue, setItemValue] = useState('')
  const [dataValue, setDataValue] = useState('')
  const [dataId, setDataId] = useState('')

  const [contractModalVisible, setContractModalVisible] = useState(false)
  const [selectedContract, setSelectedContract] = useState<any>(null)
  const [textTooltip, setTextTooltip] = useState<any>('持有机构社会信用代码')

  const [cascaderOptions, setCascaderOptions] = useState<any>([])

  const [datalist, setDatalist] = useState<any>('')
  // 身份认证
  const [dataContent, setDataContent] = useState<any>('')
  // 数据通证
  const [dataTong, setDataTong] = useState<any>('')
  // 数据目录
  const [dataDirectory, setDataDirectory] = useState<any>('')
  // 交付存证
  const [dataRecord, setDataRecord] = useState<any>('')
  // 签约交易数据
  const [signData, setSignData] = useState<any>('')
  // 合约计算订单数据
  const [orderData, setOrderData] = useState<any>('')
  // 合约计算任务数据
  const [taskData, setTaskData] = useState<any>('')
  // 任务内容数据
  const [taskContentData, setTaskContentData] = useState<any>('')
  // 计费计量数据
  const [billingData, setBillingData] = useState<any>('')
  // 当前合约ID（用于合约计算任务查询）
  const [currentContractId, setCurrentContractId] = useState<string>('')
  // 计费详情弹窗状态
  const [billingDetailModalVisible, setBillingDetailModalVisible] = useState(false)
  const [selectedBillingRecord, setSelectedBillingRecord] = useState<any>(null)
  // 收益分配数据
  const [revenueData, setRevenueData] = useState<any>('')
  // 登记挂牌数据
  const [listedVoucherData, setListedVoucherData] = useState<any>('')
  // 注册凭证数据
  const [registerVoucherData, setRegisterVoucherData] = useState<any>('')

  // 图片放大弹窗状态
  const [imageModalVisible, setImageModalVisible] = useState(false)
  const [selectedImageData, setSelectedImageData] = useState<{
    src: string
    alt: string
    data: any
    type: 'register' | 'listed'
  } | null>(null)

  // 词云图表 ref
  const wordCloudRef = useRef<HTMLDivElement>(null)

  // Define form field types
  interface SearchFormType {
    searchText: string
    category: string
    industry: string
    classification: string
  }

  // 搜索表单和分页参数管理对象
  interface SearchFormParams {
    // 搜索参数
    keyword?: string
    category?: string
    industry?: string
    categoryIndustry?: [string, string] // Cascader 的值格式 [数据分类, 行业分类]
    dataAccessLevel?: string
    searchText?: string
    contractNumber?: string
    searchTime?: [string, string] | null
    // 计费相关搜索参数
    minFee?: number
    maxFee?: number
    billingType?: string
    // 分页参数
    current: number
    pageSize: number
    total: number
  }

  // 搜索表单状态管理
  const [searchFormParams, setSearchFormParams] = useState<SearchFormParams>({
    current: 1,
    pageSize: 5,
    total: 0
  })

  // 签约交易表单参数管理
  const [signFormParams, setSignFormParams] = useState<SearchFormParams>({
    current: 1,
    pageSize: 5,
    total: 0
  })

  // 合约计算订单表单参数管理
  const [contractFormParams, setContractFormParams] = useState<SearchFormParams>({
    current: 1,
    pageSize: 5,
    total: 0
  })

  // 合约计算任务表单参数管理
  const [taskFormParams, setTaskFormParams] = useState<SearchFormParams>({
    current: 1,
    pageSize: 5,
    total: 0
  })

  // 计费计量表单参数管理
  const [billingFormParams, setBillingFormParams] = useState<SearchFormParams>({
    current: 1,
    pageSize: 5,
    total: 0
  })

  // 饼图数据 - 优化：使用真实数据
  const chartData = useMemo(
    () => [
      { name: '已交易数据', value: tradedCount || 40, itemStyle: { color: '#93DBFF' } },
      { name: '待交易数据', value: pendingCount || 20, itemStyle: { color: '#5AF3B8' } }
    ],
    [tradedCount, pendingCount]
  )

  // 词云图表配置
  const wordCloudOption = {
    series: [
      {
        type: 'wordCloud',
        // 网格大小，各项之间间距
        gridSize: 25,
        // 形状 circle 圆，cardioid  心， diamond 菱形，
        // triangle-forward 、triangle 三角，star五角星
        shape: 'circle',
        // 字体大小范围
        sizeRange: [12, 35],
        // 文字旋转角度范围
        rotationRange: [0, 0],
        // 旋转步值
        rotationStep: 90,
        // 自定义图形
        // maskImage: maskImage,
        left: 'center',
        top: 'center',
        right: null,
        bottom: null,
        // 画布宽
        width: '100%',
        // 画布高
        height: '100%',
        // 是否渲染超出画布的文字
        drawOutOfBound: true,
        textStyle: {
          color: function () {
            const colors = ['#00A085', '#3A7BC8', '#CC7700', '#5BA617', '#3BC4A8', '#b695a4', '#79ba98', '#b4b4b3']
            return colors[Math.floor(Math.random() * colors.length)]
          },
          fontWeight: 'bold'
        },
        emphasis: {
          textStyle: {
            color: '#ffffff',
            textShadow: '0 2px 8px rgba(0,0,0,0.6)'
          }
        },
        data: [
          {
            value: 11.7392043070835,
            name: '节能减排'
          },
          {
            value: 9.23723855786,
            name: '绿色低碳'
          },
          {
            value: 7.75434839431,
            name: '医疗健康'
          },
          {
            value: 11.3865516372,
            name: '数据治理'
          },
          {
            value: 7.75434839431,
            name: '智慧医疗'
          },
          {
            value: 5.83541244308,
            name: '智能制造'
          },
          {
            value: 15.83541244308,
            name: '金融服务'
          },
          {
            value: 2.83541244308,
            name: '智能排产'
          },
          {
            value: 5.83541244308,
            name: '商贸流通'
          },
          {
            value: 10.83541244308,
            name: '应急管理'
          }
        ]
      }
    ]
  }

  // 交易所数据
  const exchanges = [
    {
      dataExName: '杭州数据交易所',
      signedContractsCount: 30,
      distinctBuyerUsersCount: 10,
      listedDataCount: 1000
    },
    {
      dataExName: '郑州数据交易所',
      signedContractsCount: 30,
      distinctBuyerUsersCount: 10,
      listedDataCount: 1000
    },
    {
      dataExName: '深圳数据交易所',
      signedContractsCount: 30,
      distinctBuyerUsersCount: 10,
      listedDataCount: 1000
    },
    {
      dataExName: '北京数据交易所',
      signedContractsCount: 30,
      distinctBuyerUsersCount: 10,
      listedDataCount: 1000
    },
    {
      dataExName: '上海数据交易所',
      signedContractsCount: 30,
      distinctBuyerUsersCount: 10,
      listedDataCount: 1000
    },
    {
      dataExName: '广州数据交易所',
      signedContractsCount: 30,
      distinctBuyerUsersCount: 10,
      listedDataCount: 1000
    }
  ]

  // 数据列表
  const dataRecords = [
    {
      dataName: 'A0001111',
      registrationNo: 'A0001111',
      batchNo: 'AXX52349876',
      packageNo: 'AXX52349876',
      dataCategory: '公共数据',
      industry: '城市管理',
      dataClassification: '公开'
    },
    {
      dataName: 'A0001112',
      registrationNo: 'A0001112',
      batchNo: 'AXX52349877',
      packageNo: 'AXX52349877',
      dataCategory: '公共数据',
      industry: '城市管理',
      dataClassification: '公开'
    },
    {
      dataName: 'A0001113',
      registrationNo: 'A0001113',
      batchNo: 'AXX52349878',
      packageNo: 'AXX52349878',
      dataCategory: '公共数据',
      industry: '城市管理',
      dataClassification: '公开'
    },
    {
      dataName: 'A0001114',
      registrationNo: 'A0001114',
      batchNo: 'AXX52349879',
      packageNo: 'AXX52349879',
      dataCategory: '企业数据',
      industry: '金融',
      dataClassification: '指定机构'
    },
    {
      dataName: 'A0001115',
      registrationNo: 'A0001115',
      batchNo: 'AXX52349880',
      packageNo: 'AXX52349880',
      dataCategory: '企业数据',
      industry: '金融',
      dataClassification: '指定机构'
    }
  ]

  // 表格列定义
  const columns = [
    {
      title: '数据名称',
      dataIndex: 'dataName',
      key: 'dataName'
    },
    {
      title: '登记编号',
      dataIndex: 'dataRegisterNumber',
      key: 'dataRegisterNumber'
    },
    {
      title: '挂牌编号',
      dataIndex: 'listedNumber',
      key: 'listedNumber'
    },
    {
      title: '签约编号',
      dataIndex: 'contractNumber',
      key: 'contractNumber'
    },
    {
      title: '数据分类',
      dataIndex: 'dataCategory',
      key: 'dataCategory'
    },
    {
      title: '所属行业',
      dataIndex: 'dataSecondCategory',
      key: 'dataSecondCategory'
    },
    {
      title: '数据分级',
      dataIndex: 'dataAccessLevel',
      key: 'dataAccessLevel'
    }
  ]

  // 签约交易
  const columnsSign = [
    {
      title: '合同编号',
      dataIndex: 'dataName',
      key: 'dataName'
    },
    {
      title: '合同开始日期',
      dataIndex: 'contractStartAt',
      key: 'contractStartAt'
    },
    {
      title: '合同结束日期',
      dataIndex: 'contractEndAt',
      key: 'contractEndAt'
    },
    {
      title: '合同状态',
      dataIndex: 'status',
      key: 'status',
      render: (_: any, record: any) => <span>{record.status == 1 ? '有效' : '过期'}</span>
    },
    {
      title: '合同详情',
      dataIndex: 'dataCategory',
      key: 'dataCategory',
      render: (_: any, record: any) => (
        <a style={{ color: '#5e9b0f' }} onClick={() => handleContractDetailClick(record)}>
          查看更多
        </a>
      )
    }
  ]
  // 计量计费

  const columnsBli = [
    {
      title: '订单编号',
      dataIndex: 'contractNumber',
      key: 'contractNumber'
    },
    {
      title: '数据匿踪查询计费',
      dataIndex: 'anonymousQueryFee',
      key: 'anonymousQueryFee'
    },
    {
      title: '数据隐私求交计费',
      dataIndex: 'privacyIntersectionFee',
      key: 'privacyIntersectionFee'
    },
    {
      title: '订单算力计费',
      dataIndex: 'privacyCalculationCount',
      key: 'privacyCalculationCount'
    },

    {
      title: '费用合计',
      dataIndex: 'nodeCost',
      key: 'nodeCost',
      render: (total: number, record: any) => {
        return (
          <span
            style={{
              color: '#5e9b0f',
              fontWeight: 'bold',
              fontSize: '14px'
            }}
          >
            {record.nodeCost}
          </span>
        )
      }
    }
  ]

  const dataRecords1 = [
    {
      dataName: 'A0001111',
      registrationNo: '1000元',
      batchNo: '1000元',
      packageNo: '1000元',
      dataCategory: '公共数据',
      industry: '城市管理',
      dataClassification: '公开'
    },
    {
      dataName: 'A0001112',
      registrationNo: '1000元',
      batchNo: '1000元',
      packageNo: '1000元',
      dataCategory: '公共数据',
      industry: '城市管理',
      dataClassification: '公开'
    },
    {
      dataName: 'A0001113',
      registrationNo: '1000元',
      batchNo: '1000元',
      packageNo: '1000元',
      dataCategory: '公共数据',
      industry: '城市管理',
      dataClassification: '公开'
    },
    {
      dataName: 'A0001114',
      registrationNo: '1000元',
      batchNo: '1000元',
      packageNo: '1000元',
      dataCategory: '企业数据',
      industry: '金融',
      dataClassification: '指定机构'
    },
    {
      dataName: 'A0001115',
      registrationNo: '1000元',
      batchNo: '1000元',
      packageNo: '1000元',
      dataCategory: '企业数据',
      industry: '金融',
      dataClassification: '指定机构'
    }
  ]
  // 合约计算订单

  const columnsContract = [
    {
      title: '订单编号',
      dataIndex: 'dataName',
      key: 'dataName'
    },
    {
      title: '订单开始时间',
      dataIndex: 'contractStartAt',
      key: 'contractStartAt'
    },
    {
      title: '订单结束时间',
      dataIndex: 'contractEndAt',
      key: 'contractEndAt'
    },
    {
      title: '订单任务数量',
      dataIndex: 'taskCount',
      key: 'taskCount'
    },
    {
      title: '订单详情',
      dataIndex: 'dataCategory',
      key: 'dataCategory',
      render: (_: any, record: any) => (
        <a style={{ color: '#5e9b0f' }} onClick={() => handleIdentityClick('合约计算任务', record.contractId)}>
          查看详情
        </a>
      )
    }
  ]

  const columnsOrder = [
    {
      title: '任务编号',
      dataIndex: 'taskNumber',
      key: 'taskNumber'
    },
    {
      title: '任务完成时间',
      dataIndex: 'taskCompletionTime',
      key: 'taskCompletionTime'
    },
    {
      title: '任务交易哈希',
      dataIndex: 'transactionHash',
      key: 'transactionHash'
    },
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      render: (_: any, record: any) => {
        const getStatusText = (status: number) => {
          switch (status) {
            case 1001:
              return '执行中'
            case 1002:
              return '执行成功'
            case 1003:
              return '执行失败'
            default:
              return '未知状态'
          }
        }
        return <span>{getStatusText(record.taskStatus)}</span>
      }
    },
    {
      title: '任务详情',
      dataIndex: 'dataCategory',
      key: 'dataCategory',
      render: (_: any, record: any) => (
        <a
          style={{ color: '#5e9b0f' }}
          onClick={() => {
            handleIdentityClick('任务内容')
            getTaskContentFun(record.taskId)
          }}
        >
          查看详情
        </a>
      )
    }
  ]
  const description = 'This is a description.'
  const items = [
    {
      title: '1节点组网验证'
      // description
    },
    {
      title: '2数据签约验证'
    },
    {
      title: '3引擎交付验证'
    },
    {
      title: '4隐私计算结果'
    },
    {
      title: '5结果可信存证'
    }
  ]
  // 自动滚动功能 - 使用useCallback优化
  const startAutoScroll = useCallback(() => {
    if (scrollIntervalRef.current || isPaused) return

    scrollIntervalRef.current = setInterval(() => {
      const container = scrollContainerRef.current
      if (!container) return

      const { scrollTop, clientHeight, scrollHeight } = container

      // 使用更简单的滚动逻辑
      if (scrollHeight > clientHeight) {
        // 如果接近底部，则重置到顶部；否则继续滚动
        if (scrollTop + clientHeight >= scrollHeight - 5) {
          container.scrollTop = 0 // 重置到顶部
        } else {
          container.scrollTop += 1 // 每次增加1像素
        }
      }
    }, 50) // 优化滚动频率，减少性能消耗
  }, [isPaused])

  // 停止滚动
  const stopAutoScroll = useCallback(() => {
    if (scrollIntervalRef.current) {
      clearInterval(scrollIntervalRef.current)
      scrollIntervalRef.current = null
    }
  }, [])

  // 鼠标悬停时暂停滚动
  const handleMouseEnter = useCallback(() => {
    setIsPaused(true)
    stopAutoScroll()
  }, [stopAutoScroll])

  // 鼠标离开时恢复滚动
  const handleMouseLeave = useCallback(() => {
    setIsPaused(false)
    startAutoScroll()
  }, [startAutoScroll])

  // 显示数据登记总量弹窗
  const showDataRegistrationModal = () => {
    getDataListFun()
    getlistTreeCategory()
    setIsModalVisible(true)
  }

  // 处理分页变化
  const handlePageChange = (page: number, pageSize: number) => {
    setCurrentPage(page)
    const newParams = {
      ...searchFormParams,
      current: page,
      pageSize: pageSize
    }
    setSearchFormParams(newParams)
    // 调用API获取对应页面的数据
    getDataListFun(newParams)
  }

  // 处理弹窗关闭
  const handleModalCancel = () => {
    setIsModalVisible(false)
    setCurrentPage(1) // 重置分页状态
    setSearchFormParams(prev => ({
      ...prev,
      current: 1,
      pageSize: 5
    }))
  }

  // Form handlers
  const handleSearch = (values: any) => {
    console.log('Search form values:', values)
    // 更新搜索参数并重置分页
    const newParams = {
      ...searchFormParams,
      ...values,
      current: 1 // 搜索时重置到第一页
    }
    setSearchFormParams(newParams)
    // 调用API进行搜索
    getDataListFun(newParams)
  }

  const handleSearchFailed = (errorInfo: any) => {
    console.log('Search form failed:', errorInfo)
  }

  const handleReset = () => {
    searchForm.resetFields()
    // 重置搜索参数
    const resetParams = {
      current: 1,
      pageSize: 5,
      total: 0
    }
    setSearchFormParams(resetParams)
    // 重新查询数据（不带任何筛选条件）
    getDataListFun(resetParams)
  }

  // 签约交易表单处理函数
  const handleSignSearch = (values: any) => {
    console.log('Sign form values:', values)
    const newParams = {
      ...signFormParams,
      ...values,
      current: 1
    }
    setSignFormParams(newParams)
    // 调用签约交易API
    getSignDataFun(newParams)
  }

  const handleSignPageChange = (page: number, pageSize: number) => {
    const newParams = {
      ...signFormParams,
      current: page,
      pageSize: pageSize
    }
    setSignFormParams(newParams)
    // 调用API获取对应页面的数据
    getSignDataFun(newParams)
  }

  const handleSignReset = () => {
    signForm.resetFields()
    const resetParams = {
      current: 1,
      pageSize: 5,
      total: 0
    }
    setSignFormParams(resetParams)
    // 重新查询数据（不带任何筛选条件）
    getSignDataFun(resetParams)
  }
  console.log(billingData?.records)

  // 合约计算订单表单处理函数
  const handleContractSearch = (values: any) => {
    console.log('Contract form values:', values)
    const newParams = {
      ...contractFormParams,
      ...values,
      current: 1
    }
    setContractFormParams(newParams)
    // 调用合约计算订单API
    getOrderDataFun(newParams)
  }

  const handleContractPageChange = (page: number, pageSize: number) => {
    const newParams = {
      ...contractFormParams,
      current: page,
      pageSize: pageSize
    }
    setContractFormParams(newParams)
    // 调用API获取对应页面的数据
    getOrderDataFun(newParams)
  }

  const handleContractReset = () => {
    signForm.resetFields()
    const resetParams = {
      current: 1,
      pageSize: 5,
      total: 0
    }
    setContractFormParams(resetParams)
    // 重新查询数据（不带任何筛选条件）
    getOrderDataFun(resetParams)
  }

  // 合约计算任务表单处理函数
  const handleTaskSearch = (values: any) => {
    console.log('Task form values:', values)
    const newParams = {
      ...taskFormParams,
      ...values,
      current: 1
    }
    setTaskFormParams(newParams)
    // 调用合约计算任务API
    getTaskDataFun(newParams)
  }

  const handleTaskPageChange = (page: number, pageSize: number) => {
    const newParams = {
      ...taskFormParams,
      current: page,
      pageSize: pageSize
    }
    setTaskFormParams(newParams)
    // 调用API获取对应页面的数据
    getTaskDataFun(newParams)
  }

  const handleTaskReset = () => {
    signForm.resetFields()
    const resetParams = {
      current: 1,
      pageSize: 5,
      total: 0
    }
    setTaskFormParams(resetParams)
    // 重新查询数据（不带任何筛选条件），但保留 contractId
    getTaskDataFun(resetParams, currentContractId || undefined)
  }

  // Form instance
  const [searchForm] = Form.useForm<any>()
  const [signForm] = Form.useForm<SearchFormType>()

  // 处理行点击事件
  const handleRowClick = (record: any) => {
    setSelectedRowData(record)
    setDataValue(record.dataName)
    setDataId(record.dataId)
    setDetailModalVisible(true)
    setIsModalVisible(false)
  }

  // 关闭详情弹窗
  const handleDetailModalClose = () => {
    setDetailModalVisible(false)
  }

  // 计费计量表单处理函数
  const handleBillingSearch = (values: any) => {
    console.log('Billing form values:', values)
    const newParams = {
      ...billingFormParams,
      ...values,
      current: 1
    }
    setBillingFormParams(newParams)
    // 调用计费计量API
    getBillingDataFun(newParams)
  }

  const handleBillingPageChange = (page: number, pageSize: number) => {
    const newParams = {
      ...billingFormParams,
      current: page,
      pageSize: pageSize
    }
    setBillingFormParams(newParams)
    // 调用API获取对应页面的数据
    getBillingDataFun(newParams)
  }

  const handleBillingReset = () => {
    signForm.resetFields()
    const resetParams = {
      current: 1,
      pageSize: 5,
      total: 0
    }
    setBillingFormParams(resetParams)
    // 重新查询数据（不带任何筛选条件）
    getBillingDataFun(resetParams)
  }

  // 处理身份认证点击事件
  const handleIdentityClick = (item: any, contractId?: string) => {
    setIdentityModalVisible(true)
    // setDetailModalVisible(false)
    setItemValue(item)
    if (item === '数字身份') {
      // dataId
      setTextTooltip(
        '基于区块链技术分布式地产生全局唯一的标识符（Decentralized Identifiers），以标识各种实体机构和自然人。'
      )
      getidentity()
    } else if (item === '数据通证') {
      setTextTooltip(
        '数据通证是基于区块链等分布式账本技术生成的数字化凭证，它代表数据的所有权、使用权、经营权，并通过智能合约实现权益的交易。'
      )
      getscreenDataToken()
    } else if (item === '数据目录') {
      setTextTooltip(
        '记录数据的描述性信息，如编号、类型、来源、格式、访问权限、交易场所等，帮助用户快速定位所需数据集。'
      )
      getscreenDataDirectory()
    } else if (item === '登记挂牌') {
      setTextTooltip('')
      getListedVoucherDataFun()
      getRegisterVoucherDataFun()
    } else if (item === '签约交易') {
      setTextTooltip(
        '交易双方对签署合同的电子版进行私钥多签并存证，确保只有当满足预设条件时，交易才会被确认并自动执行，可以提升交易双方的信任度和交易效率'
      )
      getSignDataFun()
    } else if (item === '合约计算订单') {
      setTextTooltip('交易合同在交付环节转换成计算订单，智能合约记录链下计算节点间的计算规则，驱动链下节点进行隐私计算')
      getOrderDataFun()
    } else if (item === '合约计算任务') {
      setTextTooltip('计算任务是计算订单的核心载体')
      // 保存当前的 contractId
      if (contractId) {
        setCurrentContractId(contractId)
      }
      getTaskDataFun(undefined, contractId)
    } else if (item === '任务内容') {
      setTextTooltip('计算任务是计算订单的核心载体')
    } else if (item === '交付存证') {
      setTextTooltip(
        '交付存证确保数据在加工、分析、建模等计算环节中通过技术手段记录全流程证据，为数据价值分配、合规审计和纠纷解决提供可信依据'
      )
      getscreenDeliveryRecord()
    } else if (item === '计量计费') {
      setTextTooltip('计算任务是计算订单的核心载体')
      getBillingDataFun()
    } else if (item === '收益分配') {
      setTextTooltip('数据收益分配旨在通过合理机制平衡数据提供方、加工方、运营方等各参与主体的利益，推动数据价值最大化')
      getRevenueDataFun()
    }
  }

  // 关闭身份认证弹窗
  const handleIdentityModalClose = () => {
    setIdentityModalVisible(false)
  }

  // 处理图片点击事件
  const handleImageClick = (src: string, alt: string, data: any, type: 'register' | 'listed') => {
    setSelectedImageData({ src, alt, data, type })
    setImageModalVisible(true)
  }

  // 关闭图片放大弹窗
  const handleImageModalClose = () => {
    setImageModalVisible(false)
    setSelectedImageData(null)
  }

  // 注释掉重复的数据获取函数，现在通过useScreenData hook统一管理
  // const fetchData = async () => {
  //   // 数据现在通过useScreenData hook获取
  // }
  // 数据列表 - 使用本地JSON数据
  const getDataListFun = async (params?: Partial<SearchFormParams>) => {
    try {
      const searchParams = params || searchFormParams
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.dataList

      if (result) {
        setDatalist(result)
        // 更新分页参数，确保 current 和 pageSize 与请求参数一致
        setSearchFormParams(prev => ({
          ...prev,
          current: searchParams.current || 1,
          pageSize: searchParams.pageSize || 5,
          total: result.total || 0
        }))
        console.log('使用本地数据:', result)
      }
    } catch (error) {
      console.log('获取本地数据失败:', error)
    }
  }
  // 查询数据分类二级联动 - 使用本地JSON数据

  const getlistTreeCategory = async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.cascaderOptions
      console.log('获取数据分类:', result)
      if (result) {
        setCascaderOptions(result)
      }
    } catch (error) {
      message.error('获取数据分类失败，请稍后重试')
    }
  }
  // 身份认证 - 使用本地JSON数据
  const getidentity = async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.identityData
      console.log('获取身份认证数据:', result)
      if (result) {
        setDataContent(result)
      }
    } catch (error) {
      message.error('获取身份认证数据失败，请稍后重试')
    }
  }
  // 数据通证 - 使用本地JSON数据

  const getscreenDataToken = async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.tokenData
      console.log('获取数据通证:', result)
      if (result) {
        setDataTong(result)
      }
    } catch (error) {
      message.error('获取数据通证失败，请稍后重试')
    }
  }
  // 数据目录 - 使用本地JSON数据
  const getscreenDataDirectory = async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.directoryData
      console.log('获取数据目录:', result)
      if (result) {
        setDataDirectory(result)
      }
    } catch (error) {
      message.error('获取数据目录失败，请稍后重试')
    }
  }
  // 交付存证 - 使用本地JSON数据

  const getscreenDeliveryRecord = async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.deliveryRecordData
      console.log('获取交付存证数据:', result)
      if (result) {
        setDataRecord(result)
      }
    } catch (error) {
      message.error('获取交付存证数据失败，请稍后重试')
    }
  }
  // 签约交易数据 - 使用本地JSON数据
  const getSignDataFun = async (params?: Partial<SearchFormParams>) => {
    try {
      const searchParams = params || signFormParams
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.signData

      console.log('获取签约交易数据:', result)
      if (result) {
        setSignData(result)
        // 更新分页参数，确保 current 和 pageSize 与请求参数一致
        setSignFormParams(prev => ({
          ...prev,
          current: searchParams.current || 1,
          pageSize: searchParams.pageSize || 5,
          total: result.total || 0
        }))
      }
    } catch (error) {
      message.error('获取签约交易数据失败，请稍后重试')
    }
  }

  // 合约计算订单数据 - 使用本地JSON数据
  const getOrderDataFun = async (params?: Partial<SearchFormParams>) => {
    try {
      const searchParams = params || contractFormParams
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.orderData

      console.log('获取合约计算订单数据:', result)
      if (result) {
        setOrderData(result)
        // 更新分页参数，确保 current 和 pageSize 与请求参数一致
        setContractFormParams(prev => ({
          ...prev,
          current: searchParams.current || 1,
          pageSize: searchParams.pageSize || 5,
          total: result.total || 0
        }))
      }
    } catch (error) {
      message.error('获取合约计算订单数据失败，请稍后重试')
    }
  }

  // 合约计算任务数据 - 使用本地JSON数据
  const getTaskDataFun = async (params?: Partial<SearchFormParams>, contractId?: string) => {
    try {
      const searchParams = params || taskFormParams
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.taskData

      console.log('获取合约计算任务数据:', result)
      if (result) {
        setTaskData(result)
        // 更新分页参数，确保 current 和 pageSize 与请求参数一致
        setTaskFormParams(prev => ({
          ...prev,
          current: searchParams.current || 1,
          pageSize: searchParams.pageSize || 5,
          total: result.total || 0
        }))
      }
    } catch (error) {
      message.error('获取合约计算任务数据失败，请稍后重试')
    }
  }

  // 任务内容数据 - 使用本地JSON数据
  const getTaskContentFun = async (taskId: string) => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.taskContentData
      console.log('获取任务内容数据:', result)
      if (result) {
        setTaskContentData(result)
      }
    } catch (error) {
      message.error('获取任务内容数据失败，请稍后重试')
    }
  }

  // 计费计量数据 - 使用本地JSON数据
  const getBillingDataFun = async (params?: Partial<SearchFormParams>) => {
    try {
      const searchParams = params || billingFormParams
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.billingData

      console.log('获取计费计量数据:', result)
      if (result) {
        setBillingData(result)
        // 更新分页参数，确保 current 和 pageSize 与请求参数一致
        setBillingFormParams(prev => ({
          ...prev,
          current: searchParams.current || 1,
          pageSize: searchParams.pageSize || 5,
          total: result.total || 0
        }))
      }
    } catch (error) {
      message.error('获取计费计量数据失败，请稍后重试')
    }
  }

  // 收益分配数据 - 使用本地JSON数据
  const getRevenueDataFun = async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.revenueData
      console.log('获取收益分配数据:', result)
      if (result) {
        setRevenueData(result)
      }
    } catch (error) {
      message.error('获取收益分配数据失败，请稍后重试')
    }
  }

  // 登记挂牌数据 - 使用本地JSON数据
  const getListedVoucherDataFun = async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.listedVoucherData
      console.log('获取登记挂牌数据:', result)
      if (result) {
        setListedVoucherData(result)
      }
    } catch (error) {
      message.error('获取登记挂牌数据失败，请稍后重试')
    }
  }

  // 注册凭证数据 - 使用本地JSON数据
  const getRegisterVoucherDataFun = async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.registerVoucherData
      console.log('获取注册凭证数据:', result)
      if (result) {
        setRegisterVoucherData(result)
      }
    } catch (error) {
      message.error('获取注册凭证数据失败，请稍后重试')
    }
  }

  // 注释掉重复的数据获取函数，现在通过useScreenData hook统一管理
  // const getdataTrendChart = async () => {
  //   // 趋势图数据现在通过useScreenData hook获取
  // }

  // const getdataListedCount = async () => {
  //   // 挂牌数量数据现在通过useScreenData hook获取
  // }

  // const getDataContractSignCount = async () => {
  //   // 签约数量数据现在通过useScreenData hook获取
  // }
  // 注释掉重复的数据获取函数，现在通过useScreenData hook统一管理
  // const getDataExList = async () => {
  //   // 交易所列表数据现在通过useScreenData hook获取
  // }

  // const getDataCalculatedCount = async () => {
  //   // 交易统计数据现在通过useScreenData hook获取
  // }

  // 初始化词云
  const initWordCloud = () => {
    if (wordCloudRef.current) {
      const chart = echarts.init(wordCloudRef.current)
      chart.setOption(wordCloudOption)

      // 监听窗口大小变化
      const handleResize = () => {
        chart.resize()
      }
      window.addEventListener('resize', handleResize)

      // 清理函数
      return () => {
        window.removeEventListener('resize', handleResize)
        chart.dispose()
      }
    }
  }

  // 获取数据统计 - 使用优化的数据管理hook
  useEffect(() => {
    loadInitialData()
  }, [loadInitialData])

  // 初始化词云
  useEffect(() => {
    const cleanup = initWordCloud()
    return cleanup
  }, [])

  useEffect(() => {
    const updateDateTime = () => {
      const now = new Date()

      // 格式化时间为 HH:MM:SS
      const hours = now.getHours().toString().padStart(2, '0')
      const minutes = now.getMinutes().toString().padStart(2, '0')
      const seconds = now.getSeconds().toString().padStart(2, '0')
      setTime(`${hours}:${minutes}:${seconds}`)

      // 格式化日期为 YYYY.MM.DD
      const year = now.getFullYear()
      const month = (now.getMonth() + 1).toString().padStart(2, '0')
      const day = now.getDate().toString().padStart(2, '0')
      setDate(`${year}.${month}.${day}`)
    }

    // 初始化时间
    updateDateTime()

    // 每秒更新一次时间
    const timer = setInterval(updateDateTime, 1000)

    // 组件卸载时清除定时器
    return () => clearInterval(timer)
  }, [])

  // 启动自动滚动
  useEffect(() => {
    startAutoScroll()
    return () => {
      stopAutoScroll()
    }
  }, [isPaused])

  // 计算当前页数据
  const pageSize = 10
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const currentPageData = dataRecords.slice(startIndex, endIndex)
  const currentPageData1 = dataRecords1.slice(startIndex, endIndex)

  // 处理合同详情点击事件
  const handleContractDetailClick = (record: any) => {
    setSelectedContract(record)
    setContractModalVisible(true)
  }

  // 关闭合同详情弹窗
  const handleContractModalClose = () => {
    setContractModalVisible(false)
  }

  const link = () => {
    console.log(window.APP_CONFIG)
    window.open(window.APP_CONFIG.traceUrl, '_blank')
  }
  const link1 = () => {
    window.open(window.APP_CONFIG.traceUrl2, '_blank')
  }
  return (
    <div className='screen-container'>
      {/*  流星效果 */}
      <div className={styles.lineContainer}>
        <div className={styles.meteor}></div>
        <div className={styles.meteor}></div>
        <div className={styles.meteor}></div>
        <div className={styles.meteor}></div>
        <div className={styles.meteor}></div>
      </div>
      {/* 头部 */}
      <div className={styles.header}>
        <div className={styles.title}>数交链</div>
        <div className={styles.dateTime}>
          <span className={styles.time}>
            <ClockCircleOutlined style={{ marginRight: '5px' }} />
            {time}
          </span>
          <span className={styles.date}>
            <CalendarOutlined style={{ marginRight: '5px' }} />
            {date}
          </span>
        </div>
      </div>
      <div className={styles.main}>
        {/* 左侧 */}
        <div className={styles.left}>
          <div className={styles.sectionTitleBox}>
            <span></span>
            <div className={styles.sectionTitle}>数据概况统计</div>
            {/* <div className={styles.more}>查看更多</div> */}
          </div>
          <div className={styles.leftContentTitle} onClick={showDataRegistrationModal}>
            <div>
              <div className={styles.leftContentTxt} style={{ cursor: 'pointer' }}>
                数据登记总量
              </div>
              <div className={styles.leftContentNum}>{counts || 30000}</div>
            </div>

            <div className={styles.leftContentChart}>
              {/* <LineChart loading={loading} height={80} color='#18FEFE' xData={xData} yData={yData} /> */}
              <LineChart loading={loading} height={80} color='#18FEFE' xData={xData} yData={yData} />
            </div>
          </div>
          <div className={styles.leftContent}>
            <div className={styles.leftContentItem} onClick={showDataRegistrationModal}>
              <HazardInspectionCard
                title='数据挂牌数量'
                count={listedCount || 15000}
                compareRate={listedCompareRate || 50}
                mainColor='#1890FF'
                showTitle={true}
              />

              <HazardInspectionCard
                title='数据签约数量'
                count={signCount || 6000}
                compareRate={signCompareRate || 20}
                mainColor='#15DA7A'
                showTitle={true}
              />
            </div>

            {/* 数据提供方 */}
            <div className={styles.dataProvider}>
              <div className={styles.dataProviderIcon}></div>
              <div className={styles.dataProviderText}>数据提供方</div>
              <div className={styles.dataProviderImgText}>数据资源接入</div>
              <div className={styles.dataProviderImgText}>数据安全治理</div>
            </div>
          </div>

          {/* <div className={styles.leftBottom}>
            <div className={styles.sectionTitleBox}>
              <div className={styles.sectionTitle}>可信能力</div>
            </div>
            <div className={styles.capabilitiesContainer}>
              <div className={styles.capabilityRow}>
                <div className={styles.capabilityItem}>可信身份能力</div>
                <div className={styles.capabilityItem}>数据目录能力</div>
              </div>
              <div className={styles.capabilityRow}>
                <div className={styles.capabilityItem}>可信溯源能力</div>
                <div className={styles.capabilityItem}>数据治理能力</div>
              </div>
            </div>
          </div> */}
          <div className={styles.leftBottom}>
            <div className={styles.sectionTitleBox}>
              <span></span>
              <div className={styles.sectionTitle}>风险监测</div>
            </div>
            <div className={styles.capabilitiesContainer}>
              <div className={styles.capabilityRow}>
                <div className={styles.riskTypeAnalysisTitle} style={{ color: '#96c9e8' }}>
                  总览
                </div>
                <div className={styles.riskTypeAnalysis}>
                  <img src={top} alt='' />
                  <div>
                    <div>43</div>
                    <div style={{ color: '#96c9e8' }}>事件总数</div>
                  </div>
                </div>
                <div className={styles.riskTypeAnalysis}>
                  <img src={bom} alt='' />
                  <div>
                    <div>7</div>
                    <div style={{ color: '#96c9e8' }}>新增事件</div>
                  </div>
                </div>
              </div>
              <div className={styles.capabilityRight} onClick={link}>
                <div style={{ color: '#96c9e8' }}>风险类型分析</div>
                <RiskTypeChart loading={loading} />
              </div>
            </div>
          </div>
        </div>
        {/* 中间 */}
        <div className={styles.center}>
          <div className={styles.centerTitle}>运营方</div>
          {/* 轮播 */}
          <div
            className={styles.scrollContainer}
            ref={scrollContainerRef}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            <div className={styles.exchangeGrid}>
              {exchanges.map((exchange, index) => (
                <div className={styles.exchangeItem} key={index}>
                  <div className={styles.exchangeName} title={exchange.dataExName}>
                    {exchange.dataExName}
                  </div>
                  <div className={styles.exchangeStats}>
                    <div className={styles.statItem}>
                      <div className={styles.statValue}>{exchange.distinctBuyerUsersCount}</div>
                      <div className={styles.statLabel}>客户数量</div>
                    </div>
                    {/* <div className={styles.statDivider}></div> */}
                    <div className={styles.statItem}>
                      <div className={styles.statValue}>{exchange.signedContractsCount}</div>
                      <div className={styles.statLabel}>消费者方</div>
                    </div>
                    {/* <div className={styles.statDivider}></div> */}
                    <div className={styles.statItem}>
                      <div className={styles.statValue}>{exchange.listedDataCount}</div>
                      <div className={styles.statLabel}>数据目录数</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className={styles.line}></div>

          {/* 数据资源上架 */}
          <div className={styles.centerData}>
            <div className={styles.dataProviderImgText}>
              数据资源 <br /> 上架
            </div>
            <div className={styles.dataProviderImgText}>控制策略</div>
            <div className={styles.dataProviderImgText}>共享交换</div>
          </div>

          {/* 身份认证 */}
          <div className={styles.centerIdentity}>
            <div className={styles.identityImgText}>
              <span>身</span>
              <span>份</span>
              <span>认</span>
              <span>证</span>
            </div>
            <div className={styles.identityImgText}>
              <span>数</span>
              <span>据</span>
              <span>通</span>
              <span>证</span>
            </div>
            <div className={styles.identityImgText}>
              <span>数</span>
              <span>据</span>
              <span>目</span>
              <span>录</span>
            </div>
            <div className={styles.identityImgText}>
              <span>登</span>
              <span>记</span>
              <span>挂</span>
              <span>牌</span>
            </div>
            <div className={styles.identityImgText}>
              <span>签</span>
              <span>约</span>
              <span>交</span>
              <span>易</span>
            </div>
            <div className={styles.identityImgText}>
              <span>合</span>
              <span>约</span>
              <span>计</span>
              <span>算</span>
            </div>
            <div className={styles.identityImgText}>
              <span>交</span>
              <span>付</span>
              <span>存</span>
              <span>证</span>
            </div>
            <div className={styles.identityImgText}>
              <span>计</span>
              <span>量</span>
              <span>计</span>
              <span>费</span>
            </div>
            <div className={styles.identityImgText}>
              <span>收</span>
              <span>益</span>
              <span>分</span>
              <span>配</span>
            </div>
          </div>

          {/*  */}
          <div className={styles.dataProviderTitleText}>数据流通可信基础设施</div>

          <div className={styles.buttonContainer}>
            <div className={styles.platformButton}>分布式身份认证CMDID</div>
            <div className={styles.platformButton}>第三方数据交易平台</div>
          </div>

          <div className={styles.chainEngineContainer}>
            <div className={styles.verticalTextLeft}>
              <span>智</span>
              <span>能</span>
              <span>合</span>
              <span>约</span>
              <span>驱</span>
              <span>动</span>
            </div>
            <div className={styles.chain}>
              <div className={styles.chainEngineTitle}>链原生计算引擎</div>
              <div className={styles.chainProcessFlow}>
                <div className={styles.chainProcessItem}>
                  <div className={styles.chainText}>节点组网认证</div>
                  <div className={styles.chainIcon}></div>
                </div>
                <div className={styles.chainProcessItem}>
                  <div className={styles.chainText}>合约签约认证</div>
                  <div className={styles.chainIcon}></div>
                </div>
                <div className={styles.chainProcessItem}>
                  <div className={styles.chainText}>订单交付认证</div>
                  <div className={styles.chainIcon}></div>
                </div>
                <div className={styles.chainProcessItem}>
                  <div className={styles.chainText}>合约驱动算法</div>
                  <div className={styles.chainIcon}></div>
                </div>
                <div className={styles.chainProcessItem}>
                  <div className={styles.chainText}>计算结果共识</div>
                  <div className={styles.chainIcon}></div>
                </div>
              </div>
            </div>

            <div className={styles.verticalTextRight}>
              <span>数</span>
              <span>据</span>
              <span>可</span>
              <span>信</span>
              <span>托</span>
              <span>管</span>
            </div>
          </div>

          <div className={styles.dataTrust}>
            <div className={styles.centerBottomLine}></div>
            <div className={styles.centerBottom}>
              <div className={styles.dataProviderImgText}>质量标准</div>
              <div className={styles.dataProviderImgText}>增值服务</div>
            </div>
            <div className={styles.dataProviderTitleText}>数据服务方</div>
          </div>
        </div>

        {/* 右侧 */}
        <div className={styles.right}>
          <div className={styles.sectionTitleBox}>
            <span></span>
            <div className={styles.sectionTitle}>数据交易统计</div>
            {/* <div className={styles.more}>查看更多</div> */}
          </div>
          <div className={styles.rightContent}>
            <div className={styles.dataStats}>
              <div className={styles.dataItem}>
                <div className={styles.dataLabel}>数据已交易数量</div>
                <div className={styles.dataValue}>{5500}</div>
                {/* <div className={styles.dataValue}>{tradedCount}</div> */}
              </div>
              <div className={styles.dataItem}>
                <div className={styles.dataLabel}>数据待交易数量</div>
                <div className={styles.dataValue}>{500}</div>
                {/* <div className={styles.dataValue}>{pendingCount}</div> */}
              </div>
            </div>
            <div className={styles.chartContainer}>
              <Pie3DChart loading={loading} height={280} data={chartData} value={tradedCount + pendingCount} />
            </div>
          </div>

          <div className={styles.dataProviderRight}>
            <div className={styles.dataProviderImgText}>数据可信交付</div>
            <div className={styles.dataProviderImgText}>数据可信结算</div>
            <div className={styles.dataProviderText}>数据消费方</div>
            <div className={styles.dataProviderIcon}></div>
          </div>

          <div className={styles.RightBottom}>
            <div className={styles.sectionTitleBox}>
              <span></span>
              <div className={styles.sectionTitle}>数据沙箱</div>
              {/* <div className={styles.more}>查看更多</div> */}
            </div>
            <div className={styles.capabilitiesContainer}>
              <div className={styles.dataStatsRow}>
                <div className={styles.statBox}>
                  <img src={rzcs} alt='' />
                  <div>
                    <div className={styles.statLabel}>入驻厂商</div>
                    <div className={styles.statValue}>13个</div>
                  </div>
                </div>
                <div className={styles.statBox}>
                  <img src={sjzy} alt='' />
                  <div>
                    <div className={styles.statLabel}>数据资源量</div>
                    <div className={styles.statValue}>2,962个</div>
                  </div>
                </div>
                <div className={styles.statBox}>
                  <img src={sjcpl} alt='' />
                  <div>
                    <div className={styles.statLabel}>数据产品量</div>
                    <div className={styles.statValue}>321个</div>
                  </div>
                </div>
              </div>
            </div>
            {/* 数据资源趋势 */}
            <div className={styles.trendChartContainer} onClick={link1}>
              {/* <div className={styles.trendChart}>数据资源应用场景</div> */}
              {/* 词云图表 */}
              <div
                ref={wordCloudRef}
                style={{
                  width: '480px',
                  height: '250px'
                }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* 数据登记总量弹窗 */}
      <Modal
        title='数据列表'
        open={isModalVisible}
        onCancel={handleModalCancel}
        maskClosable={false}
        width='70%'
        footer={null}
        className='dataModal'
      >
        <div className={styles.dataModalContent}>
          <div className={styles.dataModalHeader}>
            <Form
              form={searchForm}
              name='dataSearchForm'
              // initialValues={{ category: '全部', industry: '全部', classification: '数据分级' }}
              onFinish={handleSearch}
              onFinishFailed={handleSearchFailed}
              layout='inline'
              className={styles.searchRow}
            >
              <div className={styles.searchFormLeft}>
                <Form.Item name='keyword'>
                  <Input
                    style={{ width: 200 }}
                    placeholder='输入关键字(数据名称和编号)'
                    className={styles.searchInput}
                  />
                </Form.Item>

                <Form.Item name='categoryIndustry'>
                  <Cascader
                    style={{ width: 200 }}
                    placeholder='选择数据分类和行业'
                    options={cascaderOptions}
                    changeOnSelect={false}
                    expandTrigger='hover'
                  />
                </Form.Item>

                <Form.Item name='dataAccessLevel'>
                  <Select style={{ width: 180 }} placeholder='按数据分级'>
                    <Select.Option value='1'>公开</Select.Option>
                    <Select.Option value='2'>指定机构</Select.Option>
                  </Select>
                </Form.Item>
              </div>

              <div className={styles.searchFormRight}>
                <Form.Item>
                  <Button onClick={handleReset} className='reset-btn'>
                    重置
                  </Button>
                </Form.Item>

                <Form.Item>
                  <Button type='primary' htmlType='submit'>
                    搜索
                  </Button>
                </Form.Item>
              </div>
            </Form>
          </div>
        </div>
        <DataTable
          columns={columns}
          dataSource={datalist.records}
          paginationProps={{
            current: searchFormParams.current,
            total: searchFormParams.total,
            pageSize: searchFormParams.pageSize,
            onChange: handlePageChange
          }}
          onRowClick={handleRowClick}
        />
      </Modal>

      {/* 数据详情弹窗 */}
      <Modal
        title={'数据名称：' + dataValue}
        open={detailModalVisible}
        onCancel={handleDetailModalClose}
        // centered
        maskClosable={false}
        footer={null}
        width='70%'
      >
        {selectedRowData && (
          <div className={styles.detailModalContent}>
            <div className={styles.centerIdentity}>
              <div className={styles.identityImgText} onClick={() => handleIdentityClick('数字身份')}>
                <span>身</span>
                <span>份</span>
                <span>认</span>
                <span>证</span>
              </div>
              <div className={styles.identityImgText} onClick={() => handleIdentityClick('数据通证')}>
                <span>数</span>
                <span>据</span>
                <span>通</span>
                <span>证</span>
              </div>
              <div className={styles.identityImgText} onClick={() => handleIdentityClick('数据目录')}>
                <span>数</span>
                <span>据</span>
                <span>目</span>
                <span>录</span>
              </div>
              <div className={styles.identityImgText} onClick={() => handleIdentityClick('登记挂牌')}>
                <span>登</span>
                <span>记</span>
                <span>挂</span>
                <span>牌</span>
              </div>
              <div className={styles.identityImgText} onClick={() => handleIdentityClick('签约交易')}>
                <span>签</span>
                <span>约</span>
                <span>交</span>
                <span>易</span>
              </div>
              <div className={styles.identityImgText} onClick={() => handleIdentityClick('合约计算订单')}>
                <span>合</span>
                <span>约</span>
                <span>计</span>
                <span>算</span>
              </div>
              <div className={styles.identityImgText} onClick={() => handleIdentityClick('交付存证')}>
                <span>交</span>
                <span>付</span>
                <span>存</span>
                <span>证</span>
              </div>
              <div className={styles.identityImgText} onClick={() => handleIdentityClick('计量计费')}>
                <span>计</span>
                <span>量</span>
                <span>计</span>
                <span>费</span>
              </div>
              <div className={styles.identityImgText} onClick={() => handleIdentityClick('收益分配')}>
                <span>收</span>
                <span>益</span>
                <span>分</span>
                <span>配</span>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* 身份认证弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex' }}>
            <div>{itemValue}</div>
            &nbsp;
            {textTooltip ? (
              <Tooltip placement='bottomLeft' title={textTooltip}>
                <QuestionCircleOutlined style={{ color: '#007eff' }} />
              </Tooltip>
            ) : (
              ''
            )}
            <div style={{ marginLeft: '20px', fontSize: '16px', height: '32px', lineHeight: '32px' }}>
              {'数据名称：' + dataValue}
            </div>
          </div>
        }
        open={identityModalVisible}
        onCancel={handleIdentityModalClose}
        maskClosable={false}
        footer={null}
        width={itemValue == '登记挂牌' ? '50' : '70%'}
        className='dataModal'
      >
        <div className={styles.identityModalContentBox}>
          {itemValue == '数字身份' && (
            <div className={styles.identityModalContent} style={{ marginTop: '150px' }}>
              {/* <BorderBox8 ref={boxRef}> */}
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>持有机构数字身份标识</div>
                <div className={styles.identityValue}>{dataContent.socialUnifiedCreditCode}</div>
              </div>
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>持有机构名称</div>
                <div className={styles.identityValue}>{dataContent.enterpriseName}</div>
              </div>
              {/* </BorderBox8> */}
            </div>
          )}
          {itemValue == '数据通证' && (
            <div className={styles.identityModalContent} style={{ marginTop: '150px' }}>
              {/* <BorderBox8 ref={boxRef1}> */}
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>数据通证 NF Token</div>
                <div className={styles.identityValue}>{dataTong.socialUnifiedCreditCode}</div>
              </div>
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>持有机构名称</div>
                <div className={styles.identityValue}>{dataTong.enterpriseName}</div>
              </div>
              {/* </BorderBox8> */}
            </div>
          )}

          {itemValue == '数据目录' && (
            <div className={styles.identityModalBox}>
              <div className={styles.identityModalContent1}>
                {/* <BorderBox8 ref={boxRef2}> */}
                <div className={styles.identityInfoRow}>
                  <div className={styles.identityLabel}>登记编号</div>
                  <div className={styles.identityValue}>{dataDirectory.dataRegisterNumber}</div>
                </div>
                <div className={styles.identityInfoRow}>
                  <div className={styles.identityLabel}>挂牌编号</div>
                  <div className={styles.identityValue}>{dataDirectory.listedNumber}</div>
                </div>
                <div className={styles.identityInfoRow}>
                  <div className={styles.identityLabel}>一级分类</div>
                  <div className={styles.identityValue}>{dataDirectory.dataFirstCategory}</div>
                </div>
                <div className={styles.identityInfoRow}>
                  <div className={styles.identityLabel}>二级分类</div>
                  <div className={styles.identityValue}>{dataDirectory.dataSecondCategory}</div>
                </div>
                {/* </BorderBox8> */}
              </div>

              <div className={styles.identityModalContent1}>
                {/* <BorderBox8 ref={boxRef3}> */}
                <div className={styles.identityInfoRow}>
                  <div className={styles.identityLabel}>数据分级</div>
                  <div className={styles.identityValue}>{dataDirectory.dataAccessLevel}</div>
                </div>
                <div className={styles.identityInfoRow}>
                  <div className={styles.identityLabel}>数据类型</div>
                  <div className={styles.identityValue}>CSV文件</div>
                </div>
                <div className={styles.identityInfoRow}>
                  <div className={styles.identityLabel}>链上哈希</div>
                  <div className={styles.identityValue}>{dataDirectory.transactionHash}</div>
                </div>
                <div className={styles.identityInfoRow}>
                  <div className={styles.identityLabel}>数据交易所</div>
                  <div className={styles.identityValue}>{dataDirectory.dataExName}</div>
                </div>
                {/* </BorderBox8> */}
              </div>
            </div>
          )}
          {itemValue == '登记挂牌' && (
            <div className={styles.certificateImageContainer}>
              <div
                className={styles.certificateImage}
                onClick={() =>
                  handleImageClick(dataRegistrationCertificate, '数据产权登记证书', registerVoucherData, 'register')
                }
              >
                <img
                  src={dataRegistrationCertificate}
                  alt='数据产权登记证书'
                  style={{ width: '100%', maxWidth: '280px', cursor: 'pointer' }}
                />
                <div className={styles.certificateImageText}>
                  <div>数据名称：{registerVoucherData?.dataName}</div>
                  <div>登记主体：{registerVoucherData?.enterpriseName}</div>
                  <div>统一社会信用代码：{registerVoucherData?.socialUnifiedCreditCode}</div>
                  <div>首次登记：{registerVoucherData?.createTime}</div>
                  <div>数据通证：{registerVoucherData?.dataRegisterNumber}</div>
                  <h3 className={styles.copyrightTitle}>数据版权信息</h3>
                  <div className={styles.copyrightTable}>
                    <table>
                      <thead>
                        <tr>
                          <th className={styles.copyrightHeader}>权益类别</th>
                          <th className={styles.copyrightHeader}>权益方</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td className={styles.copyrightLabel}>数据持有权</td>
                          <td className={styles.copyrightValue}>西昌海河文旅投资发展有限公司</td>
                        </tr>
                        <tr>
                          <td className={styles.copyrightLabel}>数据使用权</td>
                          <td className={styles.copyrightValue}>西昌海河文旅投资发展有限公司</td>
                        </tr>
                        <tr>
                          <td className={styles.copyrightLabel}>数据经营权</td>
                          <td className={styles.copyrightValue}>西昌海河文旅投资发展有限公司</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div className={styles.certificateImage}>
                <img
                  src={dataProductCertificate}
                  alt='数据产品挂牌证书'
                  style={{ width: '100%', maxWidth: '280px', cursor: 'pointer' }}
                  onClick={() =>
                    handleImageClick(dataProductCertificate, '数据产品挂牌证书', listedVoucherData, 'listed')
                  }
                />
                <div className={styles.certificateImageText}>
                  <div>挂牌编号：{listedVoucherData?.dataName}</div>
                  <div>登记主体：{listedVoucherData?.enterpriseName || ''}</div>
                  <div>统一社会信用代码：{listedVoucherData?.socialUnifiedCreditCode || ''}</div>
                  <div>首次登记：{listedVoucherData?.listedTime || ''}</div>
                </div>
              </div>
            </div>
          )}
          {/* 图片放大弹窗 */}
          {imageModalVisible && selectedImageData && (
            <div className={styles.enlargedImageContainer} onClick={handleImageModalClose}>
              <div className={styles.enlargedImageWrapper} onClick={e => e.stopPropagation()}>
                <img src={selectedImageData.src} alt={selectedImageData.alt} className={styles.enlargedImage} />
                <div className={styles.enlargedImageText}>
                  {selectedImageData.type === 'register' ? (
                    <>
                      <div>数据名称：{selectedImageData.data?.dataName}</div>
                      <div>登记主体：{selectedImageData.data?.enterpriseName}</div>
                      <div>统一社会信用代码：{selectedImageData.data?.socialUnifiedCreditCode}</div>
                      <div>首次登记：{selectedImageData.data?.createTime}</div>
                      <div>数据通证：{selectedImageData.data?.dataRegisterNumber}</div>
                      <h3 className={styles.copyrightTitle}>数据版权信息</h3>
                      <div className={styles.copyrightTable}>
                        <table>
                          <thead>
                            <tr>
                              <th className={styles.copyrightHeader}>权益类别</th>
                              <th className={styles.copyrightHeader}>权益方</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td className={styles.copyrightLabel}>数据持有权</td>
                              <td className={styles.copyrightValue}>西昌海河文旅投资发展有限公司</td>
                            </tr>
                            <tr>
                              <td className={styles.copyrightLabel}>数据使用权</td>
                              <td className={styles.copyrightValue}>西昌海河文旅投资发展有限公司</td>
                            </tr>
                            <tr>
                              <td className={styles.copyrightLabel}>数据经营权</td>
                              <td className={styles.copyrightValue}>西昌海河文旅投资发展有限公司</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </>
                  ) : (
                    <>
                      <div>挂牌编号：{selectedImageData.data?.dataName}</div>
                      <div>登记主体：{selectedImageData.data?.enterpriseName || ''}</div>
                      <div>统一社会信用代码：{selectedImageData.data?.socialUnifiedCreditCode || ''}</div>
                      <div>首次登记：{selectedImageData.data?.listedTime || ''}</div>
                    </>
                  )}
                </div>
                <button className={styles.closeButton} onClick={handleImageModalClose}>
                  ×
                </button>
              </div>
            </div>
          )}

          {itemValue == '交付存证' && (
            <div className={styles.identityModalContent}>
              {/* <BorderBox8 ref={boxRef4}> */}
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>数据订单数量</div>
                <div className={styles.identityValue}>{dataRecord.orderCount}个</div>
              </div>
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>数据任务数量</div>
                <div className={styles.identityValue}>{dataRecord.taskCount}个</div>
              </div>
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>使用方式</div>
                <div className={styles.identityValueWithBorder}>匿踪查询</div>
                <div className={styles.identityValue}>隐私求交</div>
              </div>
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>数据计量</div>
                <div className={styles.identityValueWithBorder}>{dataRecord.stealthQueryCount}次</div>
                <div className={styles.identityValue}>{dataRecord.privacyCalculationCount}次</div>
              </div>
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>算力计量</div>
                <div className={styles.identityValue}>{dataRecord.executionDays}天</div>
              </div>
              {/* </BorderBox8> */}
            </div>
          )}

          {itemValue == '签约交易' && (
            <div>
              <div className={styles.dataModalContent}>
                <div className={styles.dataModalHeader}>
                  <Form
                    form={signForm}
                    name='dataSignFormForm'
                    // initialValues={{ category: '全部', industry: '全部', classification: '数据分级' }}
                    onFinish={handleSignSearch}
                    onFinishFailed={handleSearchFailed}
                    layout='inline'
                    className={styles.searchRow}
                  >
                    <div className={styles.searchFormLeft}>
                      <Form.Item name='contractNumber'>
                        <Input placeholder='输入合同编号' className={styles.searchInput} />
                      </Form.Item>
                      <Form.Item name='searchTime'>
                        <RangePicker placeholder={['开始时间', '结束时间']} />
                      </Form.Item>
                    </div>

                    <div className={styles.searchFormRight}>
                      <Form.Item>
                        <Button onClick={handleSignReset} className='reset-btn'>
                          重置
                        </Button>
                      </Form.Item>

                      <Form.Item>
                        <Button type='primary' htmlType='submit'>
                          搜索
                        </Button>
                      </Form.Item>
                    </div>
                  </Form>
                </div>
              </div>
              <DataTable
                columns={columnsSign}
                dataSource={signData?.records || []}
                paginationProps={{
                  current: signFormParams.current,
                  total: signFormParams.total,
                  pageSize: signFormParams.pageSize,
                  onChange: handleSignPageChange
                }}
                // onRowClick={handleRowClick}
              />
            </div>
          )}
          {itemValue == '合约计算订单' && (
            <div>
              <div className={styles.dataModalContent}>
                <div className={styles.dataModalHeader}>
                  <Form
                    form={signForm}
                    name='dataContractFormForm'
                    onFinish={handleContractSearch}
                    onFinishFailed={handleSearchFailed}
                    layout='inline'
                    className={styles.searchRow}
                  >
                    <div className={styles.searchFormLeft}>
                      <Form.Item name='contractNumber'>
                        <Input placeholder='输入订单编号' className={styles.searchInput} />
                      </Form.Item>
                      <Form.Item name='searchTime'>
                        <RangePicker placeholder={['开始时间', '结束时间']} />
                      </Form.Item>
                    </div>

                    <div className={styles.searchFormRight}>
                      <Form.Item>
                        <Button onClick={handleContractReset} className='reset-btn'>
                          重置
                        </Button>
                      </Form.Item>

                      <Form.Item>
                        <Button type='primary' htmlType='submit'>
                          搜索
                        </Button>
                      </Form.Item>
                    </div>
                  </Form>
                </div>
              </div>
              <DataTable
                columns={columnsContract}
                dataSource={orderData?.records || []}
                paginationProps={{
                  current: contractFormParams.current,
                  total: contractFormParams.total,
                  pageSize: contractFormParams.pageSize,
                  onChange: handleContractPageChange
                }}
                // onRowClick={handleRowClick}
              />
            </div>
          )}

          {itemValue == '合约计算任务' && (
            <div>
              <div className={styles.dataModalContent}>
                <div className={styles.dataModalHeader}>
                  <Form
                    form={signForm}
                    name='dataTaskFormForm'
                    onFinish={handleTaskSearch}
                    onFinishFailed={handleSearchFailed}
                    layout='inline'
                    className={styles.searchRow}
                  >
                    <div className={styles.searchFormLeft}>
                      <Form.Item name='contractNumber'>
                        <Input placeholder='输入任务编号' className={styles.searchInput} />
                      </Form.Item>
                      <Form.Item name='searchTime'>
                        <RangePicker placeholder={['开始时间', '结束时间']} />
                      </Form.Item>
                    </div>

                    <div className={styles.searchFormRight}>
                      <Form.Item>
                        <Button onClick={handleTaskReset} className='reset-btn'>
                          重置
                        </Button>
                      </Form.Item>

                      <Form.Item>
                        <Button type='primary' htmlType='submit'>
                          搜索
                        </Button>
                      </Form.Item>
                    </div>
                  </Form>
                </div>
              </div>
              <DataTable
                columns={columnsOrder}
                dataSource={taskData?.records || []}
                paginationProps={{
                  current: taskFormParams.current,
                  total: taskFormParams.total,
                  pageSize: taskFormParams.pageSize,
                  onChange: handleTaskPageChange
                }}
                // onRowClick={handleRowClick}
              />
            </div>
          )}

          {itemValue == '任务内容' && (
            <div className={styles.identityModalContentR}>
              <Steps
                current={taskContentData.taskStatus == '1002' ? 5 : 4}
                labelPlacement='vertical'
                items={items}
                className={styles.steps}
              />
              <div className={styles.taskDetailInfo}>
                <div className={styles.taskDetailRow}>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>订单编号：</span>
                    <Tooltip placement='topLeft' title={taskContentData.contractNumber} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.contractNumber}</span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>需求方机构：</span>
                    <Tooltip placement='topLeft' title={taskContentData.buyerEnterpriseName} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.buyerEnterpriseName}</span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>提供方机构：</span>
                    <Tooltip placement='topLeft' title={taskContentData.sellerEnterpriseName} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.sellerEnterpriseName}</span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>区块链区块号：</span>
                    <Tooltip placement='topLeft' title={taskContentData.contractNumber} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.contractNumber}</span>
                    </Tooltip>
                  </div>
                </div>
                <div className={styles.taskDetailRow}>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>任务编号：</span>
                    <Tooltip placement='topLeft' title={taskContentData.taskNumber} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.taskNumber}</span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>需求方数据编号：</span>
                    <Tooltip placement='topLeft' title='' arrow={false}>
                      <span className={styles.taskDetailValue}></span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>提供方数据编号ID：</span>
                    <Tooltip placement='topLeft' title='' arrow={false}>
                      <span className={styles.taskDetailValue}></span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>区块链编号：</span>
                    <Tooltip placement='topLeft' title={taskContentData.blockNum} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.blockNum}</span>
                    </Tooltip>
                  </div>
                </div>
                <div className={styles.taskDetailRow}>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>任务状态：</span>
                    <Tooltip placement='topLeft' title={taskContentData.taskStatus} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.taskStatus}</span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>需求方PID：</span>
                    <Tooltip placement='topLeft' title={taskContentData.contractNumber} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.contractNumber}</span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>提供方PID：</span>
                    <Tooltip placement='topLeft' title={taskContentData.contractNumber} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.contractNumber}</span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>上链时间：</span>
                    <Tooltip placement='topLeft' title={taskContentData.contractNumber} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.contractNumber}</span>
                    </Tooltip>
                  </div>
                </div>
                <div className={styles.taskDetailRow}>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>交付开始时间：</span>
                    <Tooltip placement='topLeft' title={taskContentData.contractStartAt} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.contractStartAt}</span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>需求方mac地址：</span>
                    <Tooltip placement='topLeft' title='' arrow={false}>
                      <span className={styles.taskDetailValue}></span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>提供方mac地址：</span>
                    <Tooltip placement='topLeft' title='' arrow={false}>
                      <span className={styles.taskDetailValue}></span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>区块链交易编号：</span>
                    <Tooltip placement='topLeft' title={taskContentData.contractNumber} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.contractNumber}</span>
                    </Tooltip>
                  </div>
                </div>
                <div className={styles.taskDetailRow}>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>交付结束时间：</span>
                    <Tooltip placement='topLeft' title={taskContentData.contractEndAt} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.contractEndAt}</span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>需求方企业统一社会信用代码：</span>
                    <Tooltip placement='topLeft' title={taskContentData.buyerSocialUnifiedCreditCode} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.buyerSocialUnifiedCreditCode}</span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}>提供方企业统一社会信用代码：</span>
                    <Tooltip placement='topLeft' title={taskContentData.sellerSocialUnifiedCreditCode} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.sellerSocialUnifiedCreditCode}</span>
                    </Tooltip>
                  </div>
                  <div className={styles.taskDetailItem}>
                    <span className={styles.taskDetailLabel}></span>
                    <Tooltip placement='topLeft' title={taskContentData.contractNumber} arrow={false}>
                      <span className={styles.taskDetailValue}>{taskContentData.contractNumber}</span>
                    </Tooltip>
                  </div>
                </div>
              </div>
            </div>
          )}

          {itemValue == '计量计费' && (
            <div>
              <div className={styles.dataModalContent}>
                <div className={styles.dataModalHeader}>
                  <Form
                    form={signForm}
                    name='dataBillingFormForm'
                    onFinish={handleBillingSearch}
                    onFinishFailed={handleSearchFailed}
                    layout='inline'
                    className={styles.searchRow}
                  >
                    <div className={styles.searchFormLeft}>
                      <Form.Item name='contractNumber'>
                        <Input placeholder='输入订单编号' className={styles.searchInput} />
                      </Form.Item>
                    </div>

                    <div className={styles.searchFormRight}>
                      <Form.Item>
                        <Button onClick={handleBillingReset} className='reset-btn'>
                          重置
                        </Button>
                      </Form.Item>

                      <Form.Item>
                        <Button type='primary' htmlType='submit'>
                          搜索
                        </Button>
                      </Form.Item>
                    </div>
                  </Form>
                </div>
              </div>
              <DataTable
                columns={columnsBli}
                dataSource={billingData || []}
                paginationProps={{
                  current: billingFormParams.current,
                  total: billingFormParams.total,
                  pageSize: billingFormParams.pageSize,
                  onChange: handleBillingPageChange
                }}
              />
            </div>
          )}

          {itemValue == '收益分配' && (
            <div className={styles.identityModalContent}>
              {/* <BorderBox8 ref={boxRef5}> */}
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>收益方</div>
                <div className={styles.identityValue}>{revenueData?.buyerEnterpriseName}</div>
              </div>
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>当前数据收益</div>
                <div className={styles.identityValue}>{revenueData?.totalPrice}元</div>
              </div>
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>当前算力收益</div>
                <div className={styles.identityValue}>{revenueData?.nodeCost}元</div>
              </div>
              <div className={styles.identityInfoRow}>
                <div className={styles.identityLabel}>当前收益合计</div>
                <div className={styles.identityValue}>{+revenueData?.nodeCost + +revenueData?.totalPrice}元</div>
              </div>
              <div></div>
              {/* </BorderBox8> */}
            </div>
          )}
        </div>
      </Modal>

      {/* 合同详情弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex' }}>
            <div>合同详情</div>
            <div style={{ marginLeft: '20px', fontSize: '16px', height: '32px', lineHeight: '32px' }}>
              {'数据名称：' + dataValue}
            </div>
          </div>
        }
        open={contractModalVisible}
        onCancel={handleContractModalClose}
        footer={null}
        maskClosable={false}
        width={'50%'}
        className='dataModal'
      >
        <div className={styles.certificateImageContainer}>
          <div className={styles.certificateImage}>
            <img src={hetong} alt='合同详情' style={{ width: '100%', maxWidth: '280px' }} />
          </div>
        </div>
      </Modal>

      {/* 性能监控组件 - 仅在开发环境显示 */}
      <PerformanceMonitor enabled={process.env.NODE_ENV === 'development'} showMetrics={true} />
    </div>
  )
}

export default Screen
