import React from 'react'
import { Form, Input, Button, Card, InputNumber, Select, DatePicker, message } from 'antd'
import { useNavigate } from 'react-router-dom'

const { Option } = Select
const { RangePicker } = DatePicker

const OrderDeliveryCreate: React.FC = () => {
  const [form] = Form.useForm()
  const navigate = useNavigate()

  const onFinish = (values: any) => {
    console.log('Form values:', values)
    message.success('订单创建成功')
    navigate('/order-delivery')
  }

  return (
    <div style={{ padding: '20px' }}>
      <Card title='创建交付订单'>
        <Form
          form={form}
          layout='vertical'
          onFinish={onFinish}
          initialValues={{ paymentStatus: '待支付', paymentMethod: '在线支付' }}
        >
          <Form.Item name='orderName' label='订单名称' rules={[{ required: true, message: '请输入订单名称' }]}>
            <Input placeholder='请输入订单名称' />
          </Form.Item>

          <Form.Item name='orderNo' label='订单编号' rules={[{ required: true, message: '请输入订单编号' }]}>
            <Input placeholder='请输入订单编号' />
          </Form.Item>

          <Form.Item name='dateRange' label='有效期' rules={[{ required: true, message: '请选择有效期' }]}>
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item name='paymentAmount' label='订单金额' rules={[{ required: true, message: '请输入订单金额' }]}>
            <InputNumber min={0} precision={2} prefix='¥' style={{ width: '100%' }} placeholder='请输入订单金额' />
          </Form.Item>

          <Form.Item name='paymentStatus' label='支付状态'>
            <Select>
              <Option value='待支付'>待支付</Option>
              <Option value='已支付'>已支付</Option>
              <Option value='已完成'>已完成</Option>
            </Select>
          </Form.Item>

          <Form.Item name='paymentMethod' label='支付方式'>
            <Select>
              <Option value='在线支付'>在线支付</Option>
              <Option value='银行转账'>银行转账</Option>
              <Option value='其他方式'>其他方式</Option>
            </Select>
          </Form.Item>

          <Form.Item name='remark' label='备注'>
            <Input.TextArea rows={4} placeholder='请输入备注信息' />
          </Form.Item>

          <Form.Item>
            <Button type='primary' htmlType='submit'>
              提交
            </Button>
            <Button style={{ marginLeft: 8 }} onClick={() => navigate('/order-delivery')}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  )
}

export default OrderDeliveryCreate
