import type { MockMethod } from 'vite-plugin-mock'
import { type requestParams, resultSuccess, resultError, getRequestToken } from '../_utils'

export function createFakeUserList() {
  return [
    {
      userId: '10000',
      username: '18000000000',
      realName: 'react admin design',
      avatar: 'https://cdn.jsdelivr.net/gh/baimingxuan/media-store/images/avatar.jpeg',
      desc: 'super admin',
      password: '123456',
      token: 'fakeToken',
      homePath: '/home',
      mobile: '13800138000'
    }
  ]
}

// Mock user login
export default [
  {
    url: '/api/login',
    timeout: 500,
    method: 'post',
    response: ({ body }) => {
      const { username, password } = body

      const checkUser = createFakeUserList().find(item => item.username === username || item.mobile === username)

      if (!checkUser) {
        return resultError('手机号未注册，请先注册!')
      }

      const { userId, username: _username, token, realName, desc } = checkUser
      return resultSuccess(
        {
          userId,
          username: _username,
          token,
          realName,
          desc
        },
        { message: '登录成功' }
      )
    }
  },
  {
    url: '/api/getVerifyCode',
    timeout: 500,
    method: 'post',
    response: ({ body }) => {
      const { mobile } = body
      const checkUser = createFakeUserList().find(item => item.mobile === mobile || item.username === mobile)

      if (!checkUser) {
        return resultError('手机号未注册，请先注册!')
      }

      return resultSuccess({}, { message: '验证码发送成功' })
    }
  },
  {
    url: '/api/getUserInfo',
    method: 'get',
    response: (request: requestParams) => {
      const token = getRequestToken(request)
      if (!token) return resultError('Invalid token!')
      const checkUser = createFakeUserList().find(item => item.token === token)
      if (!checkUser) {
        return resultError('The corresponding user information was not obtained!')
      }
      return resultSuccess(checkUser)
    }
  },
  {
    url: '/api/logout',
    timeout: 200,
    method: 'get',
    response: (request: requestParams) => {
      const token = getRequestToken(request)
      if (!token) return resultError('Invalid token!')
      const checkUser = createFakeUserList().find(item => item.token === token)
      if (!checkUser) {
        return resultError('Invalid token!')
      }
      return resultSuccess(undefined, { message: 'Token has been destroyed!' })
    }
  }
] as MockMethod[]
