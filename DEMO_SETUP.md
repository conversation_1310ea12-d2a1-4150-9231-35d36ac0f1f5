# 大屏演示模式设置说明

## 概述
已成功将数交链大屏系统配置为演示模式，跳过登录页面直接展示大屏效果，所有接口请求已注释，使用本地JSON数据进行展示。

## 主要修改内容

### 1. 路由配置修改
**文件**: `src/router/index.tsx`
- 修改根路径直接跳转到大屏页面 (`/screen`)
- 注释掉登录页面路由配置

**文件**: `src/router/guard/guardRoute.tsx`
- 注释掉登录验证逻辑，允许直接访问所有页面

### 2. 数据源配置
**新增文件**: `src/data/screenData.json`
- 包含大屏展示所需的所有模拟数据
- 数据结构完整，包含：
  - 基础统计数据（counts, xData, yData等）
  - 交易所列表数据
  - 各类业务数据（身份认证、数据通证、目录等）
  - 表格数据和分页信息

### 3. 数据管理Hook优化
**文件**: `src/hooks/useScreenData.ts`
- 注释掉所有API接口调用
- 直接使用本地JSON数据初始化状态
- 移除缓存机制，简化数据加载逻辑

### 4. 大屏组件修改
**文件**: `src/views/screen/index.tsx`
- 注释掉所有接口请求导入
- 导入本地JSON数据文件
- 注释掉所有数据获取函数中的API调用
- 修改为使用本地JSON数据

## 数据结构说明

### screenData.json 主要数据字段：
```json
{
  "description": "大屏展示数据 - 用于演示效果，替代真实接口请求",
  "data": {
    "counts": "30000",                    // 数据登记总量
    "xData": [...],                       // 趋势图X轴数据
    "yData": [...],                       // 趋势图Y轴数据
    "listedCount": 15000,                 // 数据挂牌数量
    "signCount": 6000,                    // 数据签约数量
    "tradedCount": 40,                    // 已交易数据
    "pendingCount": 20,                   // 待交易数据
    "exchanges": [...],                   // 交易所列表
    "dataList": {...},                    // 数据列表
    "identityData": {...},                // 身份认证数据
    "tokenData": {...},                   // 数据通证
    "directoryData": {...},               // 数据目录
    "signData": {...},                    // 签约交易数据
    "orderData": {...},                   // 合约计算订单
    "taskData": {...},                    // 合约计算任务
    "billingData": {...},                 // 计费计量数据
    "revenueData": {...},                 // 收益分配数据
    "cascaderOptions": [...]              // 级联选择器选项
  }
}
```

## 启动方式
1. 确保项目依赖已安装：`npm install` 或 `yarn install`
2. 启动开发服务器：`npm run dev` 或 `yarn serve`
3. 访问 `http://localhost:5173/` 将直接跳转到大屏页面

## 功能特点
- ✅ 跳过登录验证，直接展示大屏
- ✅ 所有接口请求已注释，无网络依赖
- ✅ 使用本地JSON数据，展示完整功能
- ✅ 保持原有交互功能（弹窗、搜索、分页等）
- ✅ 数据实时更新效果（基于本地数据）
- ✅ 完整的大屏视觉效果

## 注意事项
1. 当前为演示模式，所有数据为静态模拟数据
2. 如需恢复正常模式，需要：
   - 取消注释路由守卫中的登录验证
   - 取消注释API接口调用
   - 移除本地JSON数据导入
3. 本地数据可根据需要在 `src/data/screenData.json` 中修改

## 技术栈
- React 18 + TypeScript
- Vite 构建工具
- Ant Design UI组件库
- ECharts 图表库
- 自定义大屏组件库

演示模式配置完成，现在可以直接访问大屏进行展示！
