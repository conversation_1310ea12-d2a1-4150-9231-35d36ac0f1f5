.login-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  background: #f5f7fa;
  background-size: cover;

  .login-box {
    display: flex;
    width: 800px;
    height: 450px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 10px 30px 0 rgba(0, 0, 1, 0.08);
    overflow: hidden;

    &-form {
      width: 100%;
    }
  }

  .login-left {
    flex: 1;
    background-color: #f0f7ff;
    background-image: url('@/assets/images/login-background.jpg');
    background-size: cover;
    background-position: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .login-right {
    flex: 1;
    padding: 30px 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .login-btn {
    width: 100%;
    height: 44px;
    border-radius: 4px;
    margin-top: 10px;
    background-color: #2b6aff;
    border-color: #2b6aff;
    font-size: 16px;
  }

  .register-btn {
    width: 100%;
    height: 44px;
    border-radius: 4px;
    font-size: 16px;
    border-color: #d9d9d9;
  }

  .login-title {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 22px;
    font-weight: bold;
    position: relative;
    padding-bottom: 10px;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 3px;
      background-color: #2b6aff;
    }
  }

  .login-title-left {
    text-align: left;
    margin-bottom: 20px;
    color: #333;
    font-size: 22px;
    font-weight: bold;
    position: relative;
    padding-bottom: 10px;
    border-bottom: 1px solid #d9d9d9;
    padding-left: 10px;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 20%;
      height: 2px;
      background-color: #2b6aff;
    }
  }

  .login-tab {
    margin-bottom: 20px;
    text-align: center;

    :global(.ant-tabs-nav) {
      margin-bottom: 24px;
    }

    :global(.ant-tabs-tab) {
      padding: 0;
      font-size: 16px;
    }

    :global(.ant-tabs-tab + .ant-tabs-tab) {
      margin-left: 40px;
    }

    :global(.ant-tabs-ink-bar) {
      background-color: #2b6aff;
    }

    :global(.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn) {
      color: #2b6aff;
      font-weight: bold;
    }
  }

  :global(.ant-input) {
    height: 44px !important;
    border-radius: 4px;
  }

  :global(.ant-input-affix-wrapper) {
    height: 44px !important;
    border-radius: 4px;
    padding: 0 11px;
    
    .ant-input {
      height: 42px !important;
    }
  }

  :global(.ant-input-password) {
    height: 44px;
    border-radius: 4px;
  }

  :global(.ant-form-item-explain-error) {
    font-size: 12px;
  }

  :global(.ant-checkbox-wrapper) {
    font-size: 12px;
    color: #666;
  }

  :global(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: #2b6aff;
    border-color: #2b6aff;
  }

  :global(a) {
    color: #2b6aff;
  }

  .no-margin {
    margin-bottom: 0;
  }

  .fr {
    float: right;
    margin-top: 5px;
    
    a {
      color: #2b6aff;
      font-size: 12px;
    }
  }
  
  .fl {
    float: left;
  }
}
