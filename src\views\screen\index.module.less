.header {
  width: 100%;
  height: 75px;

  display: flex;
  justify-content: space-between;
  // padding: 0 40px;
  color: #d8f0ff;
  position: relative;
  background: url('../../assets/images/header.png') no-repeat center 0;
  background-size: 100% 100%;
  .title {
    font-family: you<PERSON><PERSON><PERSON><PERSON><PERSON>;
    font-size: 40px;
    font-weight: 400;
    line-height: 75px;
    letter-spacing: normal;
    color: #d8f0ff;
    text-shadow:
      0px 0px 30px #0091ff,
      0px 0px 4px #0091ff;
    text-align: center;
    font-style: normal;
    text-transform: none;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .dateTime {
    display: flex;
    align-items: center;
    margin-left: auto;

    .time {
      font-size: 20px;
      margin-right: 115px;
    }

    .date {
      font-size: 20px;
      margin-right: 20px;
    }
  }
}
.sectionTitleBox {
  font-family: Alibaba-PuHuiTi-Bold;
  display: flex;
  justify-content: space-between;
  padding-left: 15px;
  margin-bottom: 5px;
  span {
    width: 4px;
    height: 45px;
    background: #1977e5;
    border-radius: 0;
    margin-right: 4px;
  }
}
.sectionTitle {
  width: 100%;
  color: #d8f0ff;
  letter-spacing: 1px;

  text-shadow:
    0px 0px 10px rgba(0, 145, 255, 0.5),
    0px 0px 4px #0091ff;
  font-size: 20px;
  font-weight: 500;
  padding: 10px 5px;
  border-radius: 2px 2px 0 0;
  display: flex;
  align-items: center;
  position: relative;
  background: url('../../assets/images/card-title-bg.png') no-repeat;
  background-size: 100% 100%;
  &::before {
    content: '';
    width: 24px;
    height: 24px;
    // background: url('../../assets/icons/vector.svg') no-repeat;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB0AAAAeCAYAAADQBxWhAAAAAXNSR0IArs4c6QAABdNJREFUSEvFl81vHFkVxX/3varqD7vddhzbTGJNIsuAsGfnP4CINVvPjv8DpJHc2bBjDTtWLMDL2SJwgB0yCCRGQphoRnhs/O12xd1dVe+9i6u627EdJ5kFUqq7Vd1VT3XuPfe8e08LH+CQD4DJO0GV0X0dhiaCqpbXygsyfo/ufvPw3wBV1eHTnneEjY5W5/LY6FS4V9+uQDrw6T+E1VWt1tw4ysDeB38LdJgFsImpzuvAFkJrdP3l6Lw0fvAapNvKEco6VwFeRdUhlIGLyFvBr0ErKrWizVwDNVZk56tclqd3DCfIQYIslMHMovv78NHsopLtKvME/oTyHcYBhHE57sv6NuhvMMyVma0Jh9uGacxRNmclVrH9YzPzcJTpq7bgunrWQmcGBAqUGp5zQhXAS8I4czbQu5RXoCNahU2EszXDD7YNLxeiU39gbatt242uxU3YnlPbrPcUmlDrBS7QNG+FVpR6YjxdPAme1pqvaH9GmXFVtJH6hoJ8DdoRtjuWNobuI8tgz2KmYpyrDXyo1SNNctWoXJ8Y8ZlkrkbdIeIwfZf28a0WBSe4ITCe9JnyYivczfY1aCme1RXL1BeWSyJOp+JecPWm8c2iCC1RnSxJjCyKUhBJH5EBXjKMZNQGeZpStBrkRDjay57lHc9mJbJwk+LXoFvPLP1dy+OdiEDM4WSSNd1kLfi2Q+dE3MciTOBMpsjAW9JEpFvgUnXJZRLlPbTZp9fLqJFTW/Qku25YXw031XwDFAtE9EnShDiyjUbD9qcw0YIv3McS+AVQF2FbLX9EzSHCiUGOncqZqpyFyF7Upu0lWTI4iBO3EB0UrOB5jpZbaazk16DbROwTM0dUxonQwDLrc7so6Lfx4We35C/8HeH3CF8r9r9q/b4WHMUxZ2RcYucy+kcFDTwvOkE6nTugHQw/xFbVKMlpUMuK+oRhMGcCT8SY75GHqiXdPcTwZy/mcwnyb0W+ipr2EB93sa8G9MkrQa3hRe5mWoGuWU62E+aJOadOUptAwxw+LDnVT4wPP3lnexN+GaLo18G5XRc1T5tZr8cEOQlVXeVT/P30njAELahT1CYo/LwXtySYT3Dhx+8CFeEvwdjPbGz/g89PyLgFelPBw5qO6eVRjN1LKop9bbLQMGdC8VTUrOLCZ28DFeFFMPzKRtEO2D3a0TndRo/HxwVPcTBsEuNtMxKSCttSCigimUnIzmoYmrh4lhCeBNXv4sNP3wA1/DWI+a0hfKlid4Oa/bhhj+nFKfP1AclRQZfAWueK2s5dUISt11uGRiuhkdbzPHlgfHgU2fA0FOHn16DC3zDmBcKeIkc2cOhETkJizpIQXaBxn143x+OG6uVWV7q9TxeXLbs7MVE7xnVrWVxr17yfBf2Wd/5HCA/Emj8QwrnHdk3MaZFL1xq5UJU0RKZXb8Q9XJwTTgva+GqfDqf+PZlurhtWNy1dLIaYfisZuLRpomQqqedt37fTRFXcWCFD5BIjl7lKXxPp1zB9fD+nfD2gKIvD7wjMVBPnLW2QccNfNvTS6Pz0IJ62kzHiGtjQpNBG4YoYG2u5DWIjWVaIU5fl9biZIz1HimPyYVEJ6KRs+FxPmjd677WCv49hfsVw+IUhL/vRTER6FvdsM25KiDEIZiCDXl01ktCwfc9gwmMvPdM4Lh96zLGraO2jo7l6K8vr0XY93p5fNbYS+AjD4qIl2RW+xvIYk563IizSao3k5FHO0nJ4BxrlZ8Hzz4PAagm4prxcCqyvln7q/iF+Y5AP/dFN9/AKwxJCiuHioTBbrj6GGZRddD9/pB9N7oUqs9I13KD07vC+1ZHGP66NWVnfrY6BZ9DaEkpDtlpOb+TLXeTpIrrzL1h+sqL0GzpyCZVCS3PGRuXM3m/Mqmwra3vD2z6/Kv9G5Q6lyv5oXZnbHPqqGw6w+r0xcoh3rMm9Q+KdTXwUyLWTrTxwZwgwcsBjVY4Dft/zbgnpmyz+f635IP9l/gfW4PU98B882QAAAABJRU5ErkJggg==')
      no-repeat;
    background-size: 100% 100%;
    // margin-right: 8px;
    display: inline-block;
  }
}
.more {
  font-family: HarmonyOS Sans SC;
  font-size: 12px;
  font-weight: 500;
  line-height: 14px;
  text-align: right;
  letter-spacing: normal;
  color: #8292a1;
  padding-top: 20px;
}
// 流星
.lineContainer {
  position: absolute;
  width: 100%;
  height: 300px;
  top: 35%;
  z-index: 999;
  overflow: hidden;
}

@keyframes meteorMove {
  0% {
    transform: translateX(-100px);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateX(calc(100vw + 100px));
    opacity: 0;
  }
}

.meteor {
  position: absolute;
  height: 6px;
  background: linear-gradient(90deg, rgba(0, 155, 255, 0.1) 0%, rgba(0, 155, 255, 1) 100%);
  border-radius: 1px;
  animation: meteorMove 5s linear infinite;
  box-shadow: 0 0 10px rgba(0, 155, 255, 0.8);
  opacity: 0;
  clip-path: polygon(0% 0%, 100% 25%, 100% 75%, 0% 100%);
  transform-origin: left center;

  &:nth-child(1) {
    width: 620px;
    top: 20%;
    animation-delay: 3s;
  }

  &:nth-child(2) {
    width: 520px;
    top: 35%;
    animation-delay: 6s;
  }

  &:nth-child(3) {
    width: 520px;
    top: 50%;
    animation-delay: 4s;
  }

  // &:nth-child(4) {
  //   width: 520px;
  //   top: 65%;
  //   animation-delay: 8s;
  // }

  // &:nth-child(5) {
  //   width: 520px;
  //   top: 80%;
  //   animation-delay: 10s;
  // }
}
// 主题
.main {
  display: flex;
  justify-content: space-between;

  .dataProvider {
    display: flex;
    align-items: center;
    width: 500px;
    height: 350px;
    margin-top: 30px;
    // border: 1px solid red;
    .dataProviderIcon {
      width: 31px;
      height: 188px;
      background: url('@/assets/iconsT/shape.svg') no-repeat;
    }
    .dataProviderText {
      width: 31px;
      height: 188px;
      margin-left: 40px;
      padding-top: 10px;
      font-family: OPPOSans;
      font-size: 30px;
      font-weight: 400;
      line-height: 35px;
      text-align: center;
      color: #d8f0ff;

      font-family: Alibaba-PuHuiTi-Bold;
      text-shadow:
        0px 0px 10px rgba(0, 145, 255, 0.5),
        0px 0px 4px #0091ff;
    }
    .dataProviderImgText {
      margin-left: 40px;
      width: 120px;
      height: 90px;
      text-align: center;
      font-family: OPPOSans;
      font-size: 18px;
      font-weight: bold;
      line-height: normal;
      text-align: center;
      letter-spacing: normal;
      color: #ffffff;
      text-shadow:
        0px 4px 10px rgba(0, 155, 255, 0.5),
        0px 0px 19px #009bff;
      background: url('@/assets/iconsT/zuo.svg') no-repeat;
      animation: glowEffect 2s ease-in-out infinite;
    }
  }
  .dataProviderRight {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 500px;
    height: 350px;
    margin-top: 10px;
    // // // border: 1px solid red;
    .dataProviderIcon {
      width: 31px;
      height: 188px;
      background: url('@/assets/iconsT/shaperight.svg') no-repeat;
    }
    .dataProviderText {
      width: 31px;
      height: 188px;
      margin-right: 40px;
      padding-top: 10px;
      font-family: OPPOSans;
      font-size: 30px;
      font-weight: 400;
      line-height: 35px;
      text-align: center;
      color: #d8f0ff;

      font-family: Alibaba-PuHuiTi-Bold;
      text-shadow:
        0px 0px 10px rgba(0, 145, 255, 0.5),
        0px 0px 4px #0091ff;
    }
    .dataProviderImgText {
      margin-right: 40px;
      width: 120px;
      height: 90px;
      text-align: center;
      font-family: OPPOSans;
      font-size: 18px;
      font-weight: bold;
      line-height: normal;
      text-align: center;
      letter-spacing: normal;
      color: #ffffff;
      text-shadow:
        0px 4px 10px rgba(0, 155, 255, 0.5),
        0px 0px 19px #009bff;
      background: url('@/assets/iconsT/zuo.svg') no-repeat;
      animation: glowEffect 2s ease-in-out infinite;
    }
  }
  .left {
    width: 500px;
    background-color: rgba(0, 10, 41, 0.5);
    border-radius: 4px;

    .leftContent {
      padding: 15px;
      display: flex;
      flex-direction: column;
      .leftContentItem {
        display: flex;
      }
    }
    .leftContentTitle {
      margin-left: 12px;
      background: url('../../assets/images/bo.png') no-repeat;
      background-size: 100% 100%;
      width: 460px;
      padding-left: 20px;
      padding-top: 10px;
      padding-bottom: 5px;
      display: flex;
      // flex-direction: column;
      justify-content: space-between;
      // position: relative;
      .leftContentTxt {
        font-size: 16px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 20px;
      }

      .leftContentNum {
        font-size: 30px;
        font-weight: 500;
        color: #129bff;
      }

      // .leftContentChart {
      //   position: absolute;
      //   right: -120px;
      //   bottom: 0;
      //   width: 120px;
      // }
    }
  }

  .leftBottom {
    padding: 15px;
    .capabilitiesContainer {
      margin-top: 20px;
      width: 100%;
      display: flex;
      .capabilityRow {
        width: 45%;
        height: 260px;
        padding: 0 25px;
        .riskTypeAnalysisTitle {
          text-align: center;
        }
        .riskTypeAnalysis {
          display: flex;
          margin-top: 30px;
          img {
            width: 80px;
            height: 80px;
            margin-right: 15px;
          }
          > div {
            padding-top: 20px;
            > div:nth-child(1) {
              margin-bottom: 10px;
            }
          }
        }
      }
      .capabilityRight {
        width: 55%;
        height: 260px;

        > div:first-child {
          text-align: center;
          margin: 5px 0;
          font-size: 16px;
          color: #fff;
        }

        #riskTypeChart {
          width: 100%;
          height: 220px;
          padding: 5px;
        }
      }
    }

    .capabilityItem {
      width: 48%;
      padding: 12px;

      border-radius: 4px;
      color: #fff;
      text-align: center;
      font-size: 16px;
      background: url('../../assets/images/capability_bg.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .center {
    width: 830px;
    .centerTitle {
      margin: 0 auto;
      width: 220px;
      height: 60px;
      font-size: 24px;
      font-weight: 350;
      line-height: 60px;
      text-align: center;
      color: #d8f0ff;
      background: url('@/assets/iconsT/title.svg') no-repeat;
      background-size: cover;
      background-position: center;
      font-family: Alibaba-PuHuiTi-Bold;
      text-shadow:
        0px 0px 10px rgba(0, 145, 255, 0.5),
        0px 0px 4px #0091ff;
    }

    .scrollContainer {
      height: 120px; /* 限制高度只显示一行 */
      overflow-y: auto;
      margin: 15px auto;
      width: 90%;
      padding: 0 10px;
      /* 隐藏滚动条但保留功能 */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }
    }

    .exchangeGrid {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      min-height: 400px; /* 确保内容高度足够触发滚动 */
    }

    .exchangeItem {
      margin-bottom: 5px;
      color: #ffffff;
      width: 48%;
      height: 130px;
      background: rgba(0, 10, 41, 0.5);
      border-radius: 4px;
      padding: 5px 20px;
      display: flex;
      flex-direction: column;
      // justify-content: space-between;
    }

    .exchangeName {
      font-size: 20px;
      font-weight: 400;
      text-align: center;
      margin-bottom: 15px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #d8f0ff;

      font-family: Alibaba-PuHuiTi-Bold;
      text-shadow:
        0px 0px 10px rgba(0, 145, 255, 0.5),
        0px 0px 4px #0091ff;
    }

    .exchangeStats {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
    }

    .statItem {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 31%;
      background: url(../../assets/images/stat_bg.png) no-repeat;
      background-size: 100% 100%;
      padding-bottom: 5px;
    }

    .statValue {
      font-size: 24px;
      font-weight: 500;
      color: #1890ff;
    }

    .statLabel {
      font-size: 14px;

      margin-top: 5px;
      color: #d8f0ff;

      font-family: Alibaba-PuHuiTi-Bold;
      text-shadow:
        0px 0px 10px rgba(0, 145, 255, 0.5),
        0px 0px 4px #0091ff;
    }

    .statDivider {
      width: 1px;
      height: 40px;
      background-color: rgba(255, 255, 255, 0.3);
    }

    .centerTop {
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 0;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 30%;
      padding: 0 15px;
    }

    .exchangeStatDiv {
      text-align: center;
      padding: 0 20px;
      width: 35%;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      &:first-child {
        border-right: 1px solid rgba(255, 255, 255, 0.1);
      }

      &:last-child {
        border-left: 1px solid rgba(255, 255, 255, 0.1);
      }

      .exchangeStatText {
        font-size: 40px;
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 5px;
        display: flex;
        justify-content: center;
        align-items: center;

        &::after {
          content: '10';
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          margin-left: 5px;
          align-self: flex-start;
          margin-top: 5px;
        }
      }

      > div:last-child {
        font-size: 14px;
        color: #8292a1;
        background-color: rgba(0, 0, 0, 0.2);
        padding: 2px 10px;
        border-radius: 4px;
      }
    }
    .centerData {
      display: flex;
      justify-content: space-evenly;
      background: url('../../assets/images/line2.png') no-repeat center 40px;
      background-size: contain;
      .dataProviderImgText {
        margin-left: 10px;
        width: 120px;
        height: 90px;
        text-align: center;
        font-family: OPPOSans;
        font-size: 18px;
        line-height: normal;
        text-align: center;
        letter-spacing: normal;
        color: #ffffff;
        text-shadow:
          0px 4px 10px rgba(0, 155, 255, 0.5),
          0px 0px 19px #009bff;
        background: url('@/assets/iconsT/zuo.svg') no-repeat center center;
        background-size: 80% 80%;
        animation: glowEffect 2s ease-in-out infinite;
      }
    }
    .centerIdentity {
      display: flex;
      justify-content: space-between;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: -70px;
        top: 95%;
        transform: translateY(-50%);
        width: 60px;
        height: 140px;
        background: url('@/assets/images/ru.png') no-repeat;
        background-size: contain;
      }

      &::after {
        content: '';
        position: absolute;
        right: -60px;
        top: 95%;
        transform: translateY(-50%);
        width: 60px;
        height: 140px;
        background: url('@/assets/images/chu.png') no-repeat;
        background-size: contain;
      }

      .identityImgText {
        width: 40px;
        height: 140px;
        background: url('@/assets/iconsT/shen.svg') no-repeat;
        font-family: 思源黑体;
        font-size: 20px;
        font-weight: bold;
        line-height: 22px;
        text-align: center;
        letter-spacing: normal;
        color: #cce0f8;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;

        // &::after {
        //   content: '';
        //   position: absolute;
        //   right: -45px;
        //   top: 50%;
        //   transform: translateY(-50%);
        //   width: 40px;
        //   height: 36px;
        //   background: url('@/assets/images/arrow_one.png') no-repeat center;
        //   background-size: contain;
        // }

        // &:last-child::after {
        //   display: none;
        // }

        span {
          display: block;
          writing-mode: vertical-lr;
          text-orientation: upright;
          margin: 2px 0;
        }
      }
    }
    .dataProviderTitleText {
      font-size: 28px;
      font-weight: 400;
      line-height: normal;
      text-align: center;
      letter-spacing: normal;
      width: 100%;
      height: 60px;
      color: #d8f0ff;

      font-family: Alibaba-PuHuiTi-Bold;
      text-shadow:
        0px 0px 10px rgba(0, 145, 255, 0.5),
        0px 0px 4px #0091ff;
    }

    .buttonContainer {
      width: 737px;
      display: flex;
      justify-content: space-between;
      margin: 20px auto;
      // padding: 0 20px;
    }

    .platformButton {
      background: url('../../assets/images/shu.png') no-repeat;
      background-size: 100% 100%;

      padding: 10px 20px;
      text-align: center;
      font-size: 16px;
      font-weight: 500;
      border-radius: 2px;
      cursor: pointer;
      width: 48%;
      transition: all 0.3s ease;
      color: #d8f0ff;

      font-family: Alibaba-PuHuiTi-Bold;
      text-shadow:
        0px 0px 10px rgba(0, 145, 255, 0.5),
        0px 0px 4px #0091ff;
      &:hover {
        box-shadow: 0px 0px 15px rgba(0, 155, 255, 0.5);
      }
    }

    .chainEngineContainer {
      width: 100%;
      // margin: 30px 0;
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .verticalTextLeft,
    .verticalTextRight {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      width: 40px;
      height: 180px;
      background: url('@/assets/iconsT/ke.png') no-repeat;
      font-family: 思源黑体;
      font-size: 20px;
      font-weight: bold;
      line-height: 22px;
      text-align: center;
      letter-spacing: normal;
      color: #cce0f8;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      span {
        display: block;
        writing-mode: vertical-lr;
        text-orientation: upright;
        margin: 2px 0;
      }
    }

    .verticalTextLeft {
      left: 0;
    }

    .verticalTextRight {
      right: 0;
    }
    .chain {
      width: 737px;
      height: 151px;
      margin: auto;
      background: url('../../assets/images/chain.png') no-repeat;
      background-size: 100% 100%;
      .chainEngineTitle {
        width: 80%;
        margin: 0 auto;

        font-size: 22px;
        font-weight: bold;
        text-align: center;
        padding: 10px 15px 10px 0;
        color: #d8f0ff;

        font-family: Alibaba-PuHuiTi-Bold;
        text-shadow:
          0px 0px 10px rgba(0, 145, 255, 0.5),
          0px 0px 4px #0091ff;
      }
    }

    .chainProcessFlow {
      display: flex;
      justify-content: space-between;
      padding: 20px 40px;
    }

    .chainProcessItem {
      display: flex;
      flex-direction: column;
      align-items: center;

      .chainIcon {
        width: 80px;
        height: 60px;
        background: url('@/assets/iconsT/yuan.png') no-repeat;
        background-size: 100% 100%;
        margin-top: 10px;
        position: relative;
        transform-style: preserve-3d;
        perspective: 1000px;
        animation: glow 3s ease-in-out infinite;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 25%;
          width: 50%;
          height: 50%;
          background: url('@/assets/iconsT/yuan1.png') no-repeat;
          background-size: 100% 100%;
          animation: springBounce 1.5s ease-in-out infinite;
          transform-origin: center center;
        }
      }

      .chainText {
        font-size: 14px;
        text-align: center;
        margin-bottom: 5px;
        color: #d8f0ff;

        font-family: Alibaba-PuHuiTi-Bold;
        text-shadow:
          0px 0px 10px rgba(0, 145, 255, 0.5),
          0px 0px 4px #0091ff;
      }
    }

    .centerBottom {
      width: 280px;
      display: flex;
      justify-content: space-between;
      margin: 0 auto;
      // padding-top: 15px;

      .dataProviderImgText {
        // margin-left: 40px;
        width: 100px;
        height: 70px;
        text-align: center;
        font-family: OPPOSans;
        font-size: 18px;
        // font-weight: bold;
        line-height: normal;
        text-align: center;
        letter-spacing: normal;
        color: #ffffff;
        text-shadow:
          0px 4px 10px rgba(0, 155, 255, 0.5),
          0px 0px 19px #009bff;
        background: url('@/assets/iconsT/zuo.svg') no-repeat center center;
        animation: glowEffect 2s ease-in-out infinite;
      }
    }
  }
  .right {
    width: 500px;
    background-color: rgba(0, 10, 41, 0.5);
    border-radius: 4px;

    .rightContent {
      display: flex;
      justify-content: space-between;
      // padding: 0 15px 15px;
      height: 220px;
      .dataStats {
        display: flex;
        flex-direction: column;
        margin-bottom: 15px;
        width: 165px;
        border: none;
        margin-top: 20px;
        margin-left: 30px;
        .dataItem {
          margin-bottom: 30px;

          .dataLabel {
            font-family: HarmonyOS Sans SC;
            font-size: 16px;
            font-weight: normal;
            line-height: 22px;
            letter-spacing: 0.8px;
            color: #ffffff;
          }

          .dataValue {
            font-family: HarmonyOS Sans SC;
            font-size: 36px;
            font-weight: normal;
            line-height: 50px;
            letter-spacing: normal;
            color: #129bff;
          }
        }
      }

      .chartContainer {
        width: 260px;
        height: 300px;
        border: none;
        :global {
          .ant-card {
            background-color: transparent !important;
          }

          .ant-card-head {
            display: none;
          }

          .ant-card-body {
            padding: 0;
          }
        }
      }
    }
  }
}
.RightBottom {
  margin-top: 15px;
  .capabilityRow {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
  }

  .capabilityItem {
    width: 48%;
    padding: 12px;
    background: rgba(18, 155, 255, 0.1);
    border-radius: 4px;
    color: #fff;
    text-align: center;
    font-size: 16px;
    box-shadow: 0 0 5px rgba(18, 155, 255, 0.3);
  }
}

@keyframes springBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes glow {
  0% {
    filter: brightness(1) drop-shadow(0 0 0px rgba(0, 155, 255, 0.7));
  }
  50% {
    filter: brightness(1.3) drop-shadow(0 0 8px rgba(0, 155, 255, 0.9));
  }
  100% {
    filter: brightness(1) drop-shadow(0 0 0px rgba(0, 155, 255, 0.7));
  }
}

@keyframes glowEffect {
  0% {
    filter: brightness(1) drop-shadow(0 0 3px rgba(0, 155, 255, 0.6));
    text-shadow:
      0px 4px 10px rgba(0, 155, 255, 0.6),
      0px 0px 19px #009bff;
  }
  50% {
    filter: brightness(1.5) drop-shadow(0 0 15px rgba(0, 155, 255, 1));
    text-shadow:
      0px 4px 15px rgba(0, 155, 255, 1),
      0px 0px 25px #00b7ff;
  }
  100% {
    filter: brightness(1) drop-shadow(0 0 3px rgba(0, 155, 255, 0.6));
    text-shadow:
      0px 4px 10px rgba(0, 155, 255, 0.6),
      0px 0px 19px #009bff;
  }
}
.dataTrust {
  background: url('../../assets/images/bombg.png') no-repeat;
  background-size: 100% 100%;
}
.line {
  width: 100%;
  height: 40px;
  background: url('../../assets/images/tuo.png') no-repeat;
  background-size: 100% 100%;
  // border: 1px solid red;
  margin: 10px auto;
}
.centerBottomLine {
  position: relative;
  top: -10px;
  width: 100%;
  height: 50px;
  background: url('../../assets/images/tuo.png') no-repeat;
  background-size: contain;
}

.dataModalContent {
  padding-top: 40px;
}

.dataModalHeader {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
}

.searchRow {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}

.searchInput {
  background-color: #00103f !important;
  border: 1px solid #00103f !important;
  color: white;
  padding: 4px 8px;
  width: 200px;
  height: 32px;
}

// 数据详情弹窗样式
.detailContainer {
  padding: 20px;
  background: rgba(4, 17, 56, 0.7);
  border-radius: 4px;
  color: white;
}

.detailItem {
  display: flex;
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 24px;
}

.detailLabel {
  width: 120px;
  font-weight: bold;
  color: #1890ff;
}

.detailValue {
  flex: 1;
}

.detailModalContent {
  width: 800px;
  margin: 150px auto;
  .centerIdentity {
    display: flex;
    justify-content: space-between;
    position: relative;

    .identityImgText {
      cursor: pointer;
      position: relative;

      width: 40px;
      height: 140px;
      background: url('@/assets/iconsT/shen.svg') no-repeat;
      font-family: 思源黑体;
      font-size: 20px;
      font-weight: bold;
      line-height: 22px;
      text-align: center;
      letter-spacing: normal;
      color: #cce0f8;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &::after {
        content: '';
        position: absolute;
        right: -45px;
        top: 50%;
        transform: translateY(-50%);
        width: 40px;
        height: 36px;
        background: url('@/assets/images/arrow_one.png') no-repeat center;
        background-size: contain;
      }

      &:last-child::after {
        display: none;
      }

      span {
        display: block;
        writing-mode: vertical-lr;
        text-orientation: upright;
        margin: 2px 0;
      }
    }
  }
  .dataProviderTitleText {
    font-family: OPPOSans;
    font-size: 28px;
    font-weight: bold;
    line-height: normal;
    text-align: center;
    letter-spacing: normal;
    color: #ffffff;
    width: 100%;
    height: 60px;
    text-shadow:
      0px 4px 10px rgba(0, 155, 255, 0.5),
      0px 0px 16px #009bff;
  }
}

// Identity Modal Styles
.identityModalContentBox {
  // display: flex;
  // justify-content: center; /* 水平居中 */
  // align-items: center; /* 垂直居中 */
  width: 100%;
  .identityModalContent {
    width: 800px;
    margin: 100px auto;
    // background: url('../../assets/images/td.png') no-repeat;
    // background-size: 100% 100%;
  }
  .identityModalContentR {
    width: 900px;
    margin: 100px auto;
  }
  .identityInfoRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #1965b2;
    padding: 15px;
    background: #061a49;
    border-left: 1px solid #1965b2;
    border-right: 1px solid #1965b2;
    // margin: 20px;

    &:first-child {
      border-top: 2px solid #1965b2;
    }

    // &:last-child {
    //   border-bottom: none;
    // }
  }

  .identityLabel {
    color: #ffffff;
    font-size: 16px;
    // font-weight: 500;
    width: 40%;
    text-align: left;
    padding-right: 20px;
  }

  .identityValue {
    text-align: center;
    font-size: 16px;
    width: 60%;
    letter-spacing: 1px;
    text-shadow: 0 0 5px rgba(24, 144, 255, 0.8);
  }

  .identityValueWithBorder {
    text-align: center;
    font-size: 16px;
    width: 60%;
    letter-spacing: 1px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: -10px;
      top: -15px;
      width: 2px;
      height: 58px;
      background: #1965b2;
    }

    &::after {
      content: '';
      position: absolute;
      right: -10px;
      top: -15px;
      width: 2px;
      height: 58px;
      background: #1965b2;
    }
  }
}

.identityModalBox {
  display: flex;
  justify-content: space-between;
  width: 900px;
  margin: 100px auto;
  .identityModalContent1 {
    width: 48%;
    .identityInfoRow {
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 2px solid #1965b2;
      padding: 15px;
      // margin: 20px;

      &:first-child {
        border-top: 2px solid #1965b2;
      }

      // &:last-child {
      //   border-bottom: none;
      // }
    }

    .identityLabel {
      color: #ffffff;
      font-size: 16px;
      // font-weight: 500;
      width: 40%;
      text-align: left;
      padding-right: 20px;
    }

    .identityValue {
      font-size: 16px;
      width: 60%;
      letter-spacing: 1px;
      text-shadow: 0 0 5px rgba(24, 144, 255, 0.8);
    }
  }
}

.certificateImageContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  // gap: 20px;
  padding: 10px;
  margin: 0 auto;
}

.certificateImage {
  min-width: 300px;
  display: flex;
  margin: 0 30px;
  position: relative;

  img {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    transition: transform 0.3s ease;

    &:hover {
      transform: scale(1.02);
      cursor: pointer;
    }
  }
  .certificateImageText {
    position: absolute;
    top: 30%;
    left: 15%;
    width: 180px;
    height: 100px;
    color: #000000;
    font-size: 8px;
    > div {
      margin: 5px 0;
    }
  }
}

/* 放大图片弹窗样式 */
.enlargedImageContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2147483647;
  padding: 20px;
  cursor: pointer;

  /* 放大弹窗中的表格样式 */
  .copyrightTitle {
    color: #000;
    font-size: 16px;
    margin: 15px 0;
    text-align: center;
  }

  .copyrightTable {
    margin-top: 15px;
    cursor: default;
    transform: none;
    width: 100%;
    min-width: 300px;

    &:hover {
      transform: none;
    }

    table {
      font-size: 12px;
      width: 100%;
      min-width: 300px;

      th,
      td {
        padding: 8px 15px;
        border: 1px solid #d0d0d0;
        white-space: nowrap;
      }

      .copyrightHeader {
        background-color: #d0d0d0;
        font-weight: bold;
        text-align: center;
        font-size: 12px;
        white-space: nowrap;
      }

      .copyrightLabel {
        background-color: #f0f0f0;
        font-weight: bold;
        width: 25%;
        text-align: center;
        white-space: nowrap;
        font-size: 12px;
      }

      .copyrightValue {
        background-color: #ffffff;
        width: 75%;
        text-align: center;
        font-size: 12px;
        white-space: nowrap;
      }
    }
  }
}

.enlargedImageWrapper {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  justify-content: center;
  cursor: default;
}

.enlargedImage {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
}

.enlargedImageText {
  position: absolute;
  top: 30%;
  left: 15%;
  width: 280px;
  height: 150px;
  color: #000000;
  font-size: 14px;
  font-weight: 500;
  padding: 10px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;

  > div {
    margin: 8px 0;
    line-height: 1.4;
  }
}

.closeButton {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;

  &:hover {
    background: #fff;
    transform: scale(1.1);
  }
}

/* 数据沙箱统计样式 */
.dataStatsRow {
  display: flex;
  justify-content: space-around;
  width: 100%;
}

.statBox {
  display: flex;
  align-items: center;
  background: rgba(0, 34, 81, 0.2);
  border-radius: 4px;
  padding: 10px;
  min-width: 120px;
}

.statBox img {
  width: 60px;
  height: 40px;
  margin-right: 10px;
}

.statValue {
  font-size: 16px;
  font-weight: 400;
  color: #d8f0ff;

  font-family: Alibaba-PuHuiTi-Bold;
  text-shadow:
    0px 0px 10px rgba(0, 145, 255, 0.5),
    0px 0px 4px #0091ff;
}

.statLabel {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.trendChartContainer {
  padding-left: 15px;
  background-color: rgba(0, 10, 41, 0.3);
  border-radius: 4px;
  height: 240px;
  position: relative;
  .trendChart {
    position: relative;
    width: 100%;
    height: 30px;
    padding-left: 20px;
    line-height: 30px;
    background: url('../../assets/images/Group.png') no-repeat;
    background-size: 100% 100%;
  }
}
.steps {
  color: #ffffff;

  :global(.ant-steps-item-title) {
    color: #ffffff !important;
  }

  :global(.ant-steps-item-description) {
    color: #ffffff !important;
  }

  :global(.ant-steps-item-content) {
    color: #ffffff !important;
  }

  :global(.ant-steps-item-title::after) {
    background-color: #ffffff !important;
  }
}

.taskDetailInfo {
  margin-top: 20px;
  padding: 15px;
  width: 100%;
  background: rgba(0, 30, 60, 0.6);
  border-radius: 6px;
}

.taskDetailRow {
  display: flex;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.taskDetailItem {
  width: 25%;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 8px;
  min-height: 24px;
}

.taskDetailLabel {
  color: #96c9e8;
  font-size: 12px;
  white-space: nowrap;
  margin-right: 5px;
  text-align: left;
  min-width: fit-content;
  flex-shrink: 0;
}

.taskDetailValue {
  color: #ffffff;
  font-size: 13px;
  line-height: 1.4;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  text-align: left;
  min-width: 0;
}

.searchFormLeft {
  display: flex;
  flex: 1;
  align-items: center;
  gap: 16px;
}

.searchFormRight {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

// 自定义 Tooltip 样式
:global(.ant-tooltip) {
  .ant-tooltip-inner {
    background-color: #061a49 !important;
    color: #ffffff;
    border: 1px solid #1965b2;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }

  .ant-tooltip-arrow {
    &::before {
      background-color: #061a49 !important;
      border: 1px solid #1965b2;
    }
  }
}

// Select 组件 placeholder 白色样式
:global(.ant-select-selector .ant-select-selection-placeholder) {
  color: #666 !important;
}

:global(.ant-select-single .ant-select-selector .ant-select-selection-placeholder) {
  color: #666 !important;
}

// 计费详情弹窗样式
.billingDetailContent {
  .detailSection {
    margin-bottom: 24px;

    h4 {
      color: #1890ff;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e6f7ff;
    }
  }

  .detailRow {
    display: flex;
    flex-wrap: wrap;
    gap: 24px;
  }

  .detailItem {
    flex: 1;
    min-width: 200px;
    display: flex;
    align-items: center;

    .detailLabel {
      color: #666;
      font-weight: 500;
      min-width: 100px;
    }

    .detailValue {
      color: #333;
      font-weight: 400;
    }
  }

  .feeBreakdown {
    background: #fafafa;
    padding: 16px;
    border-radius: 8px;

    .feeItem {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;

      .feeLabel {
        color: #666;
        font-size: 14px;
      }

      .feeValue {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }

  .remarkContent {
    background: #f6f8fa;
    padding: 12px;
    border-radius: 6px;
    color: #666;
    line-height: 1.6;
    border-left: 4px solid #1890ff;
  }
}

/* 数据版权信息标题样式 */
.copyrightTitle {
  text-align: center;
  margin: 10px 0;
}

/* 数据版权信息表格样式 */
.copyrightTable {
  margin-top: 10px;

  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 8px;
    color: #000000;

    th,
    td {
      padding: 3px 5px;
      border: 1px solid #d0d0d0;
      vertical-align: middle;
    }

    .copyrightHeader {
      background-color: #d0d0d0;
      font-weight: bold;
      text-align: center;
      font-size: 8px;
    }

    .copyrightLabel {
      background-color: #f0f0f0;
      font-weight: bold;
      width: 30%;
      text-align: center;
      white-space: nowrap;
    }

    .copyrightValue {
      background-color: #ffffff;
      width: 70%;
      text-align: center;
      font-size: 8px;
    }
  }
}
