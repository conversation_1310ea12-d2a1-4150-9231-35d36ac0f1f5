import { lazy } from '@loadable/component'
import type { RouteObject } from '../types'
import { LayoutGuard } from '../guard'
import { LazyLoad } from '@/components/LazyLoad'

// Resource route
const ResourceRoute: RouteObject = {
  path: '/resource',
  name: 'Resource',
  element: <LayoutGuard />,
  meta: {
    title: '资源登记',
    icon: 'file-text',
    orderNo: 2,
    hideChildrenInMenu: true
  },
  children: [
    {
      path: '',
      name: 'ResourcePage',
      element: LazyLoad(lazy(() => import('@/views/resource/index'))),
      meta: {
        title: '资源登记',
        key: 'resource',
        icon: 'file-text',
        orderNo: 1,
        hideMenu: true
      }
    }
  ]
}

export default ResourceRoute
