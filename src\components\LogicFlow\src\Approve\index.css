.approve-example-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.node-panel {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 70px;
  padding: 20px 10px;
  background-color: white;
  box-shadow: 0 0 10px 1px rgb(228, 224, 219);
  border-radius: 6px;
  text-align: center;
  z-index: 101;
}
.viewport {
  height: 100%;
}
.approve-node {
  display: inline-block;
  margin-bottom: 20px;
}
.node-label {
  font-size: 12px;
  margin-top: 5px;
}
.node-jugement .node-label {
  margin-top: 15px;
}
.property-panel {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 300px;
  padding: 20px;
  margin: 0;
  background-color: white;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  z-index: 101;
  box-shadow: 0 0 10px 1px rgb(228, 224, 219);
}
.property-panel-footer {
  width: 100%;
  text-align: center;
}
.property-panel-footer .property-panel-footer-hide {
  width: 200px;
}
.approve-example-container .lf-control{
  top: 10px;
  right: 10px;
  width: 275px;
  box-shadow: 0 0 10px #888888;
}
.form-property{
  width: 100%;
}
.hover-panel{
  position: fixed;
}
