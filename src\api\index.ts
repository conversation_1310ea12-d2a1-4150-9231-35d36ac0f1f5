import { service } from '@/utils/axios'
import type { LoginParams, RegisterParams } from '@/types'

// User login api
export function loginApi(data: LoginParams): Promise<any> {
  return service({
    url: '/login',
    method: 'post',
    data
  })
}

// User register api
export function registerApi(data: RegisterParams): Promise<any> {
  return service({
    url: '/register',
    method: 'post',
    data
  })
}

// Get verification code
export function getVerifyCodeApi(mobile: string): Promise<any> {
  return service({
    url: '/getVerifyCode',
    method: 'post',
    data: { mobile }
  })
}

// Get User info
export function getUserInfo(): Promise<any> {
  return service({
    url: '/getUserInfo',
    method: 'get'
  })
}

// User logout api
export function logoutApi() {
  return service({
    url: '/logout',
    method: 'get'
  })
}

// Table list
export function getTableList(params: any) {
  return service({
    url: '/table/getTableList',
    method: 'get',
    params
  })
}
