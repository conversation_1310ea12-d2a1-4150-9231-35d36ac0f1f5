import React from 'react'
import { Card, Button, Checkbox } from 'antd'
import './index.css'

const dataSourceCategories = [
  {
    label: '公共数据',
    children: ['城市管理', '环境保护', '城乡建设', '国土资源', '综合政务']
  },
  {
    label: '行业数据',
    children: ['金融行业', '制造行业', '医疗行业']
  }
]

const accessControlCategories = [
  {
    label: '公开访问：公开访问数据',
    value: 'public'
  },
  {
    label: '指定访问：指定外部合作方可访问，如重要级数据',
    value: 'designated'
  }
]

const CatalogManager = () => {
  return (
    <div className='catalog-manager-container'>
      <div className='catalog-manager-left'>
        <Card title='数据目录分类'>
          <div className='catalog-section-title'>
            数据资源分类 <Button size='small'>+</Button>
          </div>
          <div style={{ marginLeft: 16 }}>
            {dataSourceCategories.map(cat => (
              <div key={cat.label} style={{ marginBottom: 8 }}>
                <div>
                  <b>{cat.label}</b>
                </div>
                {cat.children.map(item => (
                  <div key={item} style={{ marginLeft: 16 }}>
                    <Checkbox>{item}</Checkbox>
                  </div>
                ))}
              </div>
            ))}
          </div>
          <Button type='primary' style={{ marginTop: 24 }}>
            确认
          </Button>
        </Card>
      </div>
      <div className='catalog-manager-right'>
        <Card title='数据目录分级'>
          <div className='catalog-section-title'>访问控制分级</div>
          <div style={{ marginLeft: 16 }}>
            {accessControlCategories.map(item => (
              <div key={item.value} style={{ marginBottom: 8 }}>
                <Checkbox>{item.label}</Checkbox>
              </div>
            ))}
          </div>
          <Button type='primary' style={{ marginTop: 24 }}>
            确认
          </Button>
        </Card>
      </div>
    </div>
  )
}

export default CatalogManager
