export interface PageState {
  current: number
  pageSize: number
  search?: string
}

export interface TableDataType {
  id: string | number
  serialNumber: string // 序号
  orderName: string // 订单名称
  orderNumber: string // 订单编号
  validityPeriod: string // 签约有效期
  transactionCertificate: string // 交易凭证
  orderTask: string // 订单任务
  hobby?: string[] // Used by the form
  name?: string
  phone?: string
  education?: string
}

export interface APIResult {
  list: TableDataType[]
  total: number
}
