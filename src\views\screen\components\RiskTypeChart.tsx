import { type FC, useEffect, useRef } from 'react'
import * as echarts from 'echarts/core'
import { GridComponent, TooltipComponent } from 'echarts/components'
import { Bar<PERSON>hart } from 'echarts/charts'
import { UniversalTransition } from 'echarts/features'
import { <PERSON>vasRenderer } from 'echarts/renderers'
import { PictorialBarChart } from 'echarts/charts'

// 注册必要的组件
echarts.use([GridComponent, TooltipComponent, BarChart, PictorialBarChart, UniversalTransition, CanvasRenderer])

interface RiskTypeChartProps {
  height?: number
  loading?: boolean
}

const RiskTypeChart: FC<RiskTypeChartProps> = ({ height = 220, loading = false }) => {
  const chartRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    let chartInstance: echarts.ECharts | null = null

    if (!loading && chartRef.current) {
      chartInstance = echarts.init(chartRef.current)

      const data = [70, 60, 50, 30, 10]
      const className = ['敏感数据下载', '异常数据访问', '账号公用', '账号爆破', '接口过频调用']
      const colorList = ['#39B3FF']
      // const defaultData = [100, 100, 100, 100, 100, 100]

      const option: any = {
        grid: {
          left: '5%',
          right: '5%',
          bottom: '5%',
          top: '10%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          formatter: function (params: any) {
            return (
              params[0].name +
              '<br/>' +
              "<span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:rgba(36,207,233,0.9)'></span>" +
              params[0].seriesName +
              ' : ' +
              params[0].value
            )
          }
        },
        backgroundColor: 'transparent',
        xAxis: {
          show: false,
          type: 'value'
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            axisLabel: {
              show: true,
              textStyle: {
                color: '#fff'
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            data: className
          }
        ],
        series: [
          {
            name: '风险占比',
            type: 'bar',
            zlevel: 1,
            itemStyle: {
              normal: {
                barBorderRadius: 0,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: 'rgb(57,89,255,1)'
                  },
                  {
                    offset: 1,
                    color: 'rgb(46,200,207,1)'
                  }
                ])
              }
            },
            barWidth: 20,
            data: data
          },
          {
            z: 1,
            type: 'pictorialBar',
            symbolPosition: 'end',
            // symbolRotate: '180',
            symbolSize: [20, 20],
            color: 'rgb(46,200,207,1)',
            data: data,
            symbol: 'triangle',
            symbolOffset: ['50%', 0]
          }

          // {
          //   name: '背景',
          //   type: 'bar',
          //   barWidth: 20,
          //   barGap: '-100%',
          //   data: defaultData,
          //   itemStyle: {
          //     normal: {
          //       color: '#1B375E',
          //       barBorderRadius: 0
          //     }
          //   }
          // }
        ]
      }

      chartInstance.setOption(option)

      // 监听窗口大小变化，调整图表大小
      const handleResize = () => {
        chartInstance?.resize()
      }

      window.addEventListener('resize', handleResize)
    }

    // 组件卸载时销毁图表实例
    return () => {
      if (chartInstance) {
        chartInstance.dispose()
      }
      window.removeEventListener('resize', () => {
        chartInstance?.resize()
      })
    }
  }, [loading])

  return <div ref={chartRef} style={{ width: '100%', height: `${height}px` }} />
}

export default RiskTypeChart
