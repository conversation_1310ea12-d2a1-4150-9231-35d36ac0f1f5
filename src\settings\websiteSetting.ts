// Github repo url
export const GITHUB_URL = 'https://github.com/baimingxuan/vue3-admin-design'

// Form component
// export const FORM_COMPO = {
//   name: 'Form表单',
//   url: 'https://ant-design.antgroup.com/components/form-cn',
//   desc: 'ant-design-form: 使用 ant-design 的 form 组件, 可用以收集、校验和提交数据等操作。'
// }

// Form-create plugin
export const FORM_CREATE_DESIGNER = {
  name: 'Form表单设计器',
  url: 'https://github.com/xaboy/form-create-designer/tree/next',
  desc: 'form-create-designer: 一个通过拖拽的方式快速创建表单的设计器组件, 能提高开发者对表单的开发效率, 节省开发者的时间。'
}

// Table component
export const TABLE_COMPO = {
  name: 'Table表格',
  url: 'https://ant-design.antgroup.com/components/table-cn',
  desc: 'ant-design-table: 使用 ant-design 的 table 组件, 可用于展示多条结构类似的数据, 并对其进行相关操作。'
}

// Table edit component
export const TABLE_EDIT_COMPO = {
  name: 'Table表格(可编辑行)',
  url: 'https://ant-design.antgroup.com/components/table-cn',
  desc: 'ant-design-table: 使用 ant-design 的 table 组件, 可用于展示多条结构类似的数据, 并对其进行行数据编辑操作。'
}

// Tree component
export const TREE_COMPO = {
  name: 'Tree树形控件',
  url: 'https://ant-design.antgroup.com/components/tree-cn',
  desc: 'ant-design-tree: 基于Ant-Design的Tree组件, 可以完整展现其中的层级关系, 并具有展开收起选择等交互功能。'
}

// Transfer component
export const TRANSFER_COMPO = {
  name: 'Transfer穿梭框',
  url: 'https://ant-design.antgroup.com/components/transfer-cn',
  desc: 'ant-design-transfer: 使用 ant-design 的 transfer 组件, 可用于对列表数据进行选中、取消等操作。'
}

// Upload component
export const UPLOAD_COMPO = {
  name: 'Upload图片上传组件',
  url: 'https://ant-design.antgroup.com/components/upload-cn',
  desc: 'ant-design-upload: 使用 ant-design 的 upload 组件, 并具有多种列表展示方式。'
}

// 图片上传组件资源
export const UPLOAD_IMG_SRC = 'https://cdn.jsdelivr.net/gh/baimingxuan/media-store/images/img04.jpg'
export const UPLOAD_IMG_SRC2 = 'https://cdn.jsdelivr.net/gh/baimingxuan/media-store/images/img03.jpg'

// Video player plugin
export const VIDEO_PLUGIN = {
  name: 'Video视频播放器',
  url: 'https://github.com/videojs/video.js',
  desc: 'Video.JS: 一个为 HTML5 世界构建的web视频播放器, 适用于目前主流网络视频的播放。'
}

// Video watermark component
export const VIDEO_WATERMARK = {
  name: 'Video视频水印',
  url: 'https://github.com/bokuweb/react-rnd',
  desc: '视频水印: 基于React-Rnd的拖拽功能, 在视频上通过叠加图片、文字等, 实现视频添加水印的功能。'
}

// Video resource src
export const VIDEO_RES_SRC = 'https://cdn.jsdelivr.net/gh/baimingxuan/media-store/videos/houlang.mp4'
export const VIDEO_IMG_SRC = 'https://cdn.jsdelivr.net/gh/baimingxuan/media-store/images/img06.jpg'

// Xlsx plugin
export const XLSX_PLUGIN = {
  name: 'JS-xlsx插件',
  url: 'https://github.com/SheetJS/sheetjs',
  desc: 'JS-xlsx: 由SheetJS出品的一款非常方便的只需要纯JS即可读取和导出excel的工具库, 功能强大, 支持xlsx、csv、txt等格式。'
}

// React-Sortable plugin
export const SORTABLE_PLUGIN = {
  name: 'Sortable拖拽列表',
  url: 'https://github.com/SortableJS/react-sortablejs',
  desc: 'ReactSortable: 基于Sortable.js的react组件, 用以实现拖拽功能。'
}

// React-Rnd plugin
export const REACT_RND_PLUGIN = {
  name: 'React-Rnd拖拽组件',
  url: 'https://github.com/bokuweb/react-rnd',
  desc: 'React-Rnd: 一款可调整大小和可拖拽的React组件。'
}

// React-cropper plugin
export const REACT_CROPPER_PLUGIN = {
  name: 'React-Cropper图片裁剪',
  url: 'https://github.com/react-cropper/react-cropper',
  desc: 'react-cropper: 一个优雅的图片裁剪插件, 可实现图片裁剪、图片生成等功能, 并支持生成png、jpeg、webp等图片格式。'
}

// Image-composition component
export const IMAGE_COMPOSITION = {
  name: 'Image图片合成',
  url: 'https://github.com/bokuweb/react-rnd',
  desc: '图片合成: 基于React-Rnd的拖拽功能, 在其上通过叠加图片、文字等, 实现图片的叠加，并合成新图片的功能。'
}

// Cropper image src
export const CROPPER_IMG_SRC = 'https://cdn.jsdelivr.net/gh/baimingxuan/media-store/images/img02.jpg'

// Compress image src
export const COMPRESS_IMG_SRC = 'https://cdn.jsdelivr.net/gh/baimingxuan/media-store/images/img03.jpg'

// Composition image src
export const COMPOSITION_IMG_SRC = 'https://cdn.jsdelivr.net/gh/baimingxuan/media-store/images/img01.jpg'
export const COMPOSITION_IMG_SRC2 = 'https://cdn.jsdelivr.net/gh/baimingxuan/media-store/images/img05.jpeg'

// Image-compress component
export const IMAGE_COMPRESS = {
  name: 'Image图片压缩',
  url: 'https://github.com/baimingxuan/react-admin-design/main/src/views/image/image-compress.tsx',
  desc: 'ImageCompress: 纯JS实现对图片的等比压缩和放大的功能, 并能对图片进行下载。'
}

// Count-to plugin
export const COUNTUP_PLUGIN = {
  name: 'CountUp数字滚动',
  url: 'https://github.com/glennreyes/react-countup',
  desc: 'React-CountUp: 一个无依赖、轻量级的react数字滚动插件, 以更有趣的方式显示数字数据。'
}

// React-CodeMirror plugin
export const CODEMIRROR_PLUGIN = {
  name: 'CodeMirror代码编辑器',
  url: 'https://github.com/uiwjs/react-codemirror',
  desc: 'React-CodeMirror: 是一款基于 react 的代码编辑器, 可支持html、javascript、typescript等。'
}

// Antv-g6 plugin
export const ANTV_G6_PLUGIN = {
  name: 'AntV-G6流程图',
  url: 'https://github.com/antvis/g6',
  desc: 'antv-g6: 一个图可视化引擎。它提供了图的绘制、布局、分析、交互、动画等图可视化的基础能力。'
}

// ReactTreeOrg plugin
export const React_TREE_ORG_PLUGIN = {
  name: 'Tree-Org树状组织图',
  url: 'https://github.com/artdong/react-org-tree',
  desc: 'react-tree-org: 基于 react 的树结构组织图, 可用于公司组织架构展示。'
}

// WangEditor plugin
export const WANG_EDITOR_PLUGIN = {
  name: '富文本编辑器',
  url: 'https://github.com/wangeditor-team/wangEditor',
  desc: 'wangEditor: 基于javascript和css开发的 Web富文本编辑器, 轻量、简洁、易用、开源免费。'
}

// React-Markdown-Editor plugin
export const MARKDOWN_EDITOR_PLUGIN = {
  name: 'Markdown编辑器',
  url: 'https://github.com/uiwjs/react-md-editor',
  desc: 'React-Md-Editor: 是一款基于 react 的 Markdown 编辑器,比较适合博客系统。'
}

// export const FLOW_EDITOR_PLUGIN = {
//   name: '流程图编辑器',
//   url: 'https://github.com/didi/LogicFlow',
//   desc: 'LogicFlow: 是一款流程图编辑框架，提供了一系列流程图交互、编辑所必需的功能和灵活的节点自定义、插件等拓展机制。'
// }

export const RESULT_COMPO = {
  name: 'Result结果',
  url: 'https://ant-design.antgroup.com/components/result-cn',
  desc: 'ant-design-result: 使用 ant-design 的 result 组件, 可用于反馈一系列操作任务的处理结果。'
}
