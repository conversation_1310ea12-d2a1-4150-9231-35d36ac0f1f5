import type { EChartsOption } from 'echarts'

export const countUpData = [
  {
    title: '今日点击',
    icon: 'location',
    count: 682,
    color: '#1890ff'
  },
  {
    title: '新增用户',
    icon: 'person',
    count: 259,
    color: '#fa541c'
  },
  {
    title: '信息发送',
    icon: 'message',
    count: 1262,
    color: '#faad14'
  },
  {
    title: '点赞统计',
    icon: 'like',
    count: 508,
    color: '#13c2c2'
  },
  {
    title: '累计收藏',
    icon: 'heart',
    count: 379,
    color: '#722ed1'
  }
]

export const pieOptions: EChartsOption = {
  tooltip: {
    trigger: 'item'
  },
  legend: {
    bottom: 0,
    left: 'center'
  },
  series: [
    {
      name: '访问来源',
      type: 'pie',
      radius: '70%',
      center: ['50%', '45%'],
      color: ['#1890ff', '#fa541c', '#faad14', '#13c2c2', '#722ed1'],
      data: [
        { value: 1620, name: '直接访问' },
        { value: 1169, name: '邮件营销' },
        { value: 986, name: '联盟广告' },
        { value: 624, name: '视频广告' },
        { value: 857, name: '搜索引擎' }
      ],
      roseType: 'radius',
      animationType: 'scale',
      animationEasing: 'exponentialInOut',
      animationDelay: function () {
        return Math.random() * 400
      }
    }
  ]
}

export const ringOptions: EChartsOption = {
  tooltip: {
    trigger: 'item'
  },
  legend: {
    bottom: 0,
    left: 'center'
  },
  series: [
    {
      color: ['#1890ff', '#fa541c', '#faad14', '#13c2c2', '#722ed1'],
      name: '访问来源',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['50%', '45%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '12',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 1620, name: '直接访问' },
        { value: 1169, name: '邮件营销' },
        { value: 986, name: '联盟广告' },
        { value: 624, name: '视频广告' },
        { value: 2758, name: '搜索引擎' }
      ],
      animationType: 'scale',
      animationEasing: 'exponentialInOut',
      animationDelay: function () {
        return Math.random() * 100
      }
    }
  ]
}

export const radarOptions: EChartsOption = {
  legend: {
    bottom: 0,
    data: ['推广渠道', '广告投放', '访问来源']
  },
  radar: {
    radius: '70%',
    center: ['50%', '45%'],
    splitNumber: 8,
    indicator: [
      {
        name: '直接访问'
      },
      {
        name: '邮件营销'
      },
      {
        name: '联盟广告'
      },
      {
        name: '视频广告'
      },
      {
        name: '搜索引擎'
      }
    ]
  },
  series: [
    {
      type: 'radar',
      symbolSize: 0,
      areaStyle: {
        shadowBlur: 0,
        shadowColor: 'rgba(0,0,0,.2)',
        shadowOffsetX: 0,
        shadowOffsetY: 10,
        opacity: 1
      },
      data: [
        {
          value: [1920, 1920, 1920, 0, 0],
          name: '推广渠道',
          itemStyle: {
            color: '#1890ff'
          }
        },
        {
          value: [1920, 0, 0, 1920, 1920],
          name: '访问来源',
          itemStyle: {
            color: '#722ed1'
          }
        },
        {
          value: [920, 920, 920, 920, 920],
          name: '广告投放',
          itemStyle: {
            color: '#faad14'
          }
        }
      ]
    }
  ]
}

export const barOptions: EChartsOption = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      lineStyle: {
        width: 1,
        color: '#fa541c'
      }
    }
  },
  grid: {
    left: 0,
    right: '1%',
    top: '2%',
    bottom: 0,
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    axisTick: {
      alignWithLabel: true
    }
  },
  yAxis: {
    type: 'value',
    max: value => {
      return Math.ceil(value.max / 100) * 100 + 300
    }
  },
  label: {
    show: true,
    fontSize: 14,
    color: '#1890ff',
    position: 'top',
    formatter: '{c}'
  },
  series: [
    {
      type: 'bar',
      name: '访问量',
      barWidth: '40%',
      color: ['#1890ff'],
      data: [782, 925, 1196, 812, 328, 223, 1080]
    }
  ]
}

export const lineOptions: EChartsOption = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      lineStyle: {
        width: 1,
        color: '#fa541c'
      }
    }
  },
  grid: {
    left: 0,
    right: '1%',
    top: '2%',
    bottom: 0,
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    axisTick: {
      alignWithLabel: true
    }
  },
  yAxis: {
    type: 'value',
    max: value => {
      return Math.ceil(value.max / 100) * 100 + 300
    }
  },
  label: {
    show: true,
    fontSize: 14,
    color: '#722ed1',
    position: 'top',
    formatter: '{c}'
  },
  series: [
    {
      type: 'line',
      name: '访问量',
      color: ['#722ed1'],
      smooth: true,
      data: [782, 925, 1196, 812, 328, 223, 1080]
    }
  ]
}

export const pie3DOptions: EChartsOption = {
  backgroundColor: 'transparent',
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    bottom: 0,
    left: 'center',
    textStyle: {
      color: '#888'
    }
  },
  graphic: [
    {
      type: 'group',
      rotation: Math.PI / 4,
      bounding: 'raw',
      right: 110,
      bottom: 110,
      z: 0,
      children: [
        {
          type: 'rect',
          left: 'center',
          top: 'center',
          z: 1,
          shape: {
            width: 400,
            height: 50
          },
          style: {
            fill: 'rgba(0,0,0,0.1)'
          }
        }
      ]
    }
  ],
  series: [
    {
      name: '访问来源',
      type: 'pie',
      radius: ['35%', '65%'],
      center: ['50%', '40%'],
      avoidLabelOverlap: false,
      startAngle: 45,
      color: [
        'rgba(24, 144, 255, 0.9)',
        'rgba(250, 84, 28, 0.9)',
        'rgba(250, 173, 20, 0.9)',
        'rgba(19, 194, 194, 0.9)',
        'rgba(114, 46, 209, 0.9)'
      ],
      data: [
        { value: 1620, name: '直接访问' },
        { value: 1169, name: '邮件营销' },
        { value: 986, name: '联盟广告' },
        { value: 624, name: '视频广告' },
        { value: 857, name: '搜索引擎' }
      ],
      roseType: 'radius',
      itemStyle: {
        borderRadius: 5,
        borderColor: 'rgba(255, 255, 255, 0.6)',
        borderWidth: 2,
        opacity: 0.9,
        shadowBlur: 15,
        shadowColor: 'rgba(0, 0, 0, 0.2)',
        shadowOffsetX: 2,
        shadowOffsetY: 2
      },
      label: {
        show: false
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 20,
          shadowOffsetX: 5,
          shadowOffsetY: 5,
          shadowColor: 'rgba(0, 0, 0, 0.3)',
          opacity: 1
        },
        label: {
          show: true,
          fontSize: '14',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      animationType: 'scale',
      animationEasing: 'elasticOut',
      animationDelay: function (idx) {
        return Math.random() * 200
      },
      zlevel: 10
    },
    // 第二层，底部阴影效果
    {
      name: '',
      type: 'pie',
      silent: true,
      z: 1,
      zlevel: 1,
      radius: ['35%', '65%'],
      center: ['51%', '41%'],
      startAngle: 45,
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 1620, name: '' },
        { value: 1169, name: '' },
        { value: 986, name: '' },
        { value: 624, name: '' },
        { value: 857, name: '' }
      ],
      itemStyle: {
        color: 'rgba(0, 0, 0, 0.15)',
        borderWidth: 0
      },
      animation: false
    },
    // 第三层，底部阴影效果加深
    {
      name: '',
      type: 'pie',
      silent: true,
      z: 0,
      zlevel: 0,
      radius: ['35%', '65%'],
      center: ['52%', '42%'],
      startAngle: 45,
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 1620, name: '' },
        { value: 1169, name: '' },
        { value: 986, name: '' },
        { value: 624, name: '' },
        { value: 857, name: '' }
      ],
      itemStyle: {
        color: 'rgba(0, 0, 0, 0.1)',
        borderWidth: 0
      },
      animation: false
    }
  ]
}
