import React, { useEffect, useRef } from 'react'
import './index.less'

interface MeteorProps {
  width?: number
  height?: number
  meteorCount?: number
  meteorSpeed?: number
  className?: string
  style?: React.CSSProperties
  backgroundColor?: string
}

const MeteorShower: React.FC<MeteorProps> = ({
  width = window.innerWidth,
  height = window.innerHeight,
  meteorCount = 20,
  meteorSpeed = 1,
  className = '',
  style = {},
  backgroundColor = 'rgba(0, 0, 0, 1)'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置画布尺寸
    canvas.width = width
    canvas.height = height

    // 流星对象数组
    const meteors: {
      x: number
      y: number
      length: number
      speed: number
      width: number
      opacity: number
      trail: { x: number; y: number }[]
    }[] = []

    // 初始化流星
    const initMeteors = () => {
      for (let i = 0; i < meteorCount; i++) {
        meteors.push(createMeteor())
      }
    }

    // 创建单个流星对象
    const createMeteor = () => {
      const y = Math.random() * height
      return {
        x: 0, // 从左侧开始
        y, // 随机高度
        length: Math.random() * 80 + 40, // 随机长度
        speed: (Math.random() * 5 + 2) * meteorSpeed, // 随机速度
        width: Math.random() * 2 + 0.5, // 随机宽度
        opacity: Math.random() * 0.7 + 0.3, // 随机透明度
        trail: [{ x: 0, y }] // 初始轨迹点
      }
    }

    // 绘制流星
    const drawMeteor = (meteor: (typeof meteors)[0]) => {
      // 绘制主要流星线
      ctx.beginPath()
      const gradient = ctx.createLinearGradient(meteor.x, meteor.y, meteor.x + meteor.length, meteor.y)
      gradient.addColorStop(0, `rgba(255, 255, 255, ${meteor.opacity})`)
      gradient.addColorStop(1, 'rgba(255, 255, 255, 0)')
      ctx.strokeStyle = gradient
      ctx.lineWidth = meteor.width
      ctx.moveTo(meteor.x, meteor.y)
      ctx.lineTo(meteor.x + meteor.length, meteor.y)
      ctx.stroke()
      ctx.closePath()

      // 绘制尾迹
      if (meteor.trail.length > 1) {
        ctx.beginPath()
        ctx.strokeStyle = `rgba(255, 255, 255, ${meteor.opacity * 0.3})`
        ctx.lineWidth = meteor.width * 0.7
        for (let i = 0; i < meteor.trail.length - 1; i++) {
          const point = meteor.trail[i]
          const nextPoint = meteor.trail[i + 1]
          ctx.moveTo(point.x, point.y)
          ctx.lineTo(nextPoint.x, nextPoint.y)
        }
        ctx.stroke()
        ctx.closePath()
      }
    }

    // 更新流星位置
    const updateMeteors = () => {
      // 添加半透明矩形而不是完全清除，形成拖尾效果
      ctx.fillStyle = backgroundColor.replace(/[^,]+(?=\))/, '0.1')
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      meteors.forEach((meteor, index) => {
        // 移动流星
        meteor.x += meteor.speed

        // 更新轨迹
        meteor.trail.push({ x: meteor.x, y: meteor.y })
        // 限制轨迹长度
        if (meteor.trail.length > 5) {
          meteor.trail.shift()
        }

        // 如果流星移出画布，重置到左侧
        if (meteor.x - meteor.length > canvas.width) {
          meteors[index] = createMeteor()
        }

        // 绘制流星
        drawMeteor(meteor)
      })

      requestAnimationFrame(updateMeteors)
    }

    // 初始设置背景
    ctx.fillStyle = backgroundColor
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // 启动流星动画
    initMeteors()
    updateMeteors()

    // 清理函数
    return () => {
      // 取消动画帧请求
      // 实际上浏览器会在组件卸载时自动取消animationFrame
    }
  }, [width, height, meteorCount, meteorSpeed, backgroundColor])

  return (
    <canvas
      ref={canvasRef}
      className={`meteor-shower ${className}`}
      style={{ ...style, position: 'absolute', top: 0, left: 0, zIndex: -1 }}
    />
  )
}

export { MeteorShower }
