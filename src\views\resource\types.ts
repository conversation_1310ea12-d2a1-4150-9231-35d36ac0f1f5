export interface PageState {
  current: number
  pageSize: number
  search?: string
  dataType?: string
  dataLevel?: string
}

export interface APIResult {
  list: DataRegistrationType[]
  total: number
}

export interface DataRegistrationType {
  id: string
  dataName: string
  registrationNumber: string
  registrationEntity: string
  registrationTime: string
  dataType: string
  dataLevel: string
  registrationCertificate: string
  connectStatus: boolean
}

export interface ResourceDataType {
  id: string
  resourceName: string
  resourceType: string
  registrant: string
  registrationTime: string
  description: string
  status: boolean
}
