import { lazy } from '@loadable/component'
import type { RouteObject } from '../types'
import { LayoutGuard } from '../guard'
import { LazyLoad } from '@/components/LazyLoad'

const OrderDeliveryRoute: RouteObject = {
  path: '/order',
  name: 'OrderDelivery',
  element: <LayoutGuard />,
  meta: {
    title: '订单交付',
    icon: 'shopping-cart',
    orderNo: 12
  },
  children: [
    {
      path: '',
      name: 'OrderDeliveryList',
      element: LazyLoad(lazy(() => import('@/views/order'))),
      meta: {
        title: '订单交付',
        key: 'order',
        icon: 'shopping-cart',
        orderNo: 1
      }
    },
    {
      path: 'create',
      name: 'OrderDeliveryCreate',
      element: LazyLoad(lazy(() => import('@/views/order/create'))),
      meta: {
        title: '创建订单',
        key: 'order-create',
        hideMenu: true
      }
    },
    {
      path: 'detail/:id',
      name: 'OrderDeliveryDetail',
      element: LazyLoad(lazy(() => import('@/views/order/detail'))),
      meta: {
        title: '订单详情',
        key: 'order-detail',
        hideMenu: true
      }
    }
  ]
}

export default OrderDeliveryRoute
