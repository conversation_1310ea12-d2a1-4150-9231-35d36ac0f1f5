import { type FC, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Form, Input, Button, message } from 'antd'
import styles from './index.module.less'

const ResetPage: FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [countdown, setCountdown] = useState(0)

  const navigate = useNavigate()

  // 手机号码验证规则
  const validatePhone = (_: any, value: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!value) {
      return Promise.reject('请输入手机号码')
    }
    if (!phoneRegex.test(value)) {
      return Promise.reject('请输入正确的手机号码格式')
    }
    return Promise.resolve()
  }

  const handleSendCode = () => {
    form
      .validateFields(['phone'])
      .then(() => {
        message.success('验证码发送成功！')
        let count = 60
        setCountdown(count)
        const timer = setInterval(() => {
          count--
          setCountdown(count)
          if (count === 0) {
            clearInterval(timer)
          }
        }, 1000)
      })
      .catch(error => {
        console.log(error)
      })
  }

  const handleReset = async (values: any) => {
    try {
      setLoading(true)
      // 模拟重置密码API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      message.success('密码重置成功！')
      navigate('/login')
    } catch (error) {
      message.error((error as unknown as Error).message)
    } finally {
      setLoading(false)
    }
  }

  const handleBack = () => {
    navigate('/login')
  }

  return (
    <div className={styles['login-wrapper']}>
      <div className={styles['login-box']}>
        <div className={styles['login-left']}></div>
        <div className={styles['login-right']}>
          <div className={styles['login-title-left']}>重置密码</div>
          <Form
            form={form}
            initialValues={{
              phone: '',
              code: ''
            }}
            className={styles['login-box-form']}
            onFinish={handleReset}
          >
            <Form.Item name='phone' rules={[{ validator: validatePhone }]}>
              <Input placeholder='请输入手机号' maxLength={11} />
            </Form.Item>
            <Form.Item name='code' rules={[{ required: true, message: '请输入验证码' }]}>
              <Input
                placeholder='请输入验证码'
                maxLength={6}
                suffix={
                  <Button
                    type='link'
                    style={{
                      padding: 0,
                      height: '100%',
                      color: '#2b6aff',
                      display: 'flex',
                      alignItems: 'center',
                      marginRight: '-7px'
                    }}
                    disabled={countdown > 0}
                    onClick={handleSendCode}
                  >
                    {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
                  </Button>
                }
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 10 }}>
              <Button type='primary' htmlType='submit' className={styles['login-btn']} loading={loading}>
                提交
              </Button>
            </Form.Item>
            <div style={{ textAlign: 'right' }}>
              <a onClick={handleBack} style={{ fontSize: '12px', color: '#2b6aff' }}>
                登录
              </a>
            </div>
          </Form>
        </div>
      </div>
    </div>
  )
}

export default ResetPage
