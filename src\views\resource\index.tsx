import type { ColumnsType } from 'antd/es/table'
import { type FC, useState, useEffect } from 'react'
import {
  type TableProps,
  Card,
  Button,
  Table,
  Tag,
  Modal,
  Space,
  Form,
  Input,
  Select,
  Radio,
  Upload,
  Checkbox,
  InputNumber
} from 'antd'
import { PlusOutlined, CloudUploadOutlined } from '@ant-design/icons'
import { getTableList } from '@/api'
import type { APIResult, PageState, DataRegistrationType } from './types'
import styles from './style.module.css'

const dataTypeOptions = [{ label: '企业数据', value: '企业数据' }]

const dataLevelOptions = [
  { label: '公开可见', value: '公开可见' },
  { label: '指定机构可见', value: '指定机构可见' }
]

const firstLevelOptions = [{ label: '一级分类', value: '一级分类' }]

const secondLevelOptions = [{ label: '二级分类', value: '二级分类' }]

const ResourceTable: FC = () => {
  const [tableLoading, setTableLoading] = useState(false)
  const [tableData, setTableData] = useState<DataRegistrationType[]>([])
  const [tableTotal, setTableTotal] = useState<number>(0)
  const [tableQuery, setTableQuery] = useState<PageState>({ current: 1, pageSize: 10 })
  const [modalVisible, setModalVisible] = useState(false)
  const [form] = Form.useForm()

  const columns: ColumnsType<DataRegistrationType> = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      render: (_: any, __: any, idx: number) => idx + 1 + (tableQuery.current - 1) * tableQuery.pageSize
    },
    {
      title: '数据名称',
      dataIndex: 'dataName',
      align: 'center'
    },
    {
      title: '登记编号',
      dataIndex: 'registrationNumber',
      align: 'center'
    },
    {
      title: '登记主体',
      dataIndex: 'registrationEntity',
      align: 'center'
    },
    {
      title: '登记时间',
      dataIndex: 'registrationTime',
      align: 'center'
    },
    {
      title: '数据分类',
      dataIndex: 'dataType',
      align: 'center'
    },
    {
      title: '数据分级',
      dataIndex: 'dataLevel',
      align: 'center'
    }
    // {
    //   title: '登记凭证',
    //   dataIndex: 'registrationCertificate',
    //   align: 'center',
    //   render: () => <Button type='link'>查看详情</Button>
    // },
    // {
    //   title: '挂接状态',
    //   dataIndex: 'connectStatus',
    //   align: 'center',
    //   render: (value: boolean) => (
    //     <Tag color={value ? 'green' : 'gray'} className={styles['status-tag']}>
    //       {value ? '在线' : '离线'}
    //     </Tag>
    //   )
    // }
  ]

  useEffect(() => {
    fetchData()
  }, [tableQuery])

  async function fetchData() {
    setTableLoading(true)
    try {
      const data = await getTableList(tableQuery)
      const { list, total } = data as unknown as APIResult
      setTableData(list)
      setTableTotal(total)
    } catch (error) {
      console.error('Failed to fetch data', error)
      // Mock data for development
      const mockData: DataRegistrationType[] = [
        {
          id: '1',
          dataName: '用户行为统计分析',
          registrationNumber: 'SZDEXRE021740326401',
          registrationEntity: '西昌海河交旅投资发展有限公司',
          registrationTime: '2022-01-01 15:43:33',
          dataType: '企业数据',
          dataLevel: '公开可见',
          registrationCertificate: '查看详情',
          connectStatus: true
        },
        {
          id: '2',
          dataName: '用户行为统计分析',
          registrationNumber: 'SZDEXRE021740326401',
          registrationEntity: '西昌海河交旅投资发展有限公司',
          registrationTime: '2022-01-01 15:43:33',
          dataType: '企业数据',
          dataLevel: '指定机构可见',
          registrationCertificate: '查看详情',
          connectStatus: false
        },
        {
          id: '3',
          dataName: '用户行为统计分析',
          registrationNumber: 'SZDEXRE021740326401',
          registrationEntity: '西昌海河交旅投资发展有限公司',
          registrationTime: '2022-01-01 15:43:33',
          dataType: '企业数据',
          dataLevel: '指定机构可见',
          registrationCertificate: '查看详情',
          connectStatus: true
        }
      ]
      setTableData(mockData)
      setTableTotal(mockData.length)
    }
    setTableLoading(false)
  }

  function handlePageChange(page: number, pageSize: number) {
    setTableQuery({ ...tableQuery, current: page, pageSize })
  }

  const handleRegister = () => {
    setModalVisible(true)
  }

  const handleModalCancel = () => {
    setModalVisible(false)
  }

  const handleModalSubmit = () => {
    form
      .validateFields()
      .then(values => {
        console.log('Form values:', values)
        // Process form submission
        setModalVisible(false)
      })
      .catch(error => {
        console.error('Validation failed:', error)
      })
  }

  const uploadProps = {
    name: 'file',
    action: '/api/upload',
    onChange(info: any) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList)
      }
      if (info.file.status === 'done') {
        console.log(`${info.file.name} file uploaded successfully`)
      } else if (info.file.status === 'error') {
        console.error(`${info.file.name} file upload failed.`)
      }
    }
  }

  const uploadButton = (
    <div>
      <CloudUploadOutlined />
      <div>将文件拖到此处，或选择文件上传</div>
    </div>
  )

  return (
    <Card bordered={false}>
      <Space style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <Form layout='inline' onFinish={values => setTableQuery({ ...tableQuery, ...values, current: 1 })}>
          <Form.Item name='search'>
            <Input placeholder='数据名称/登记编号' style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name='dataType'>
            <Select placeholder='数据分类' options={dataTypeOptions} allowClear style={{ width: 120 }} />
          </Form.Item>
          <Form.Item name='dataLevel'>
            <Select placeholder='数据分级' options={dataLevelOptions} allowClear style={{ width: 120 }} />
          </Form.Item>
          <Form.Item>
            <Button type='primary' htmlType='submit'>
              查询
            </Button>
          </Form.Item>
          <Form.Item>
            <Button
              htmlType='button'
              onClick={() => {
                setTableQuery({ current: 1, pageSize: 10 })
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
        <Button type='primary' icon={<PlusOutlined rev={undefined} />} onClick={handleRegister}>
          资源登记
        </Button>
      </Space>
      <Table
        rowKey='id'
        columns={columns}
        dataSource={tableData}
        loading={tableLoading}
        pagination={{
          current: tableQuery.current,
          pageSize: tableQuery.pageSize,
          total: tableTotal,
          showTotal: total => `共 ${total} 条`,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          onChange: handlePageChange
        }}
      />

      <Modal
        title='数据登记'
        open={modalVisible}
        onCancel={handleModalCancel}
        onOk={handleModalSubmit}
        width={800}
        bodyStyle={{ maxHeight: '600px', overflow: 'auto', padding: '24px 40px' }}
        footer={[
          <Button key='cancel' onClick={handleModalCancel}>
            取消
          </Button>,
          <Button key='submit' type='primary' onClick={handleModalSubmit}>
            登记
          </Button>
        ]}
      >
        <Form
          form={form}
          layout='horizontal'
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          className={styles.registrationForm}
        >
          <Form.Item label='数据登记主体：' name='registrationEntity' className={styles.formItem}>
            <Input defaultValue='西昌海河交旅投资发展有限公司' disabled />
          </Form.Item>

          <Form.Item label='统一社会信用代码：' name='creditCode' className={styles.formItem}>
            <Input defaultValue='91513401MA62H8AK1U' disabled />
          </Form.Item>

          <Form.Item
            label='资源名称：'
            name='resourceName'
            className={styles.formItem}
            rules={[{ required: true, message: '请输入资源名称' }]}
            extra={<span className={styles.charCount}>0/20</span>}
          >
            <Input defaultValue='用户行为统计分析' />
          </Form.Item>

          <Form.Item
            label='资源简述：'
            name='resourceDescription'
            className={styles.formItem}
            rules={[{ required: true, message: '请输入资源简述' }]}
            extra={<span className={styles.charCount}>0/200</span>}
          >
            <Input.TextArea rows={3} defaultValue='基于环海湖公园景点用户数据加工后形成的行为分析统计数据' />
          </Form.Item>

          <Form.Item label='产权类型：' name='propertyRightType' className={styles.formItem}>
            <Checkbox.Group className={styles.checkboxGroup}>
              <Checkbox value='dataOwnership'>数据所有权</Checkbox>
              <Checkbox value='dataUsageRight'>数据使用权</Checkbox>
              <Checkbox value='dataOperatingRight'>数据经营权</Checkbox>
            </Checkbox.Group>
          </Form.Item>

          <div className={styles.formRow}>
            <Form.Item
              label='数据分类：'
              name='dataType'
              className={styles.formItem}
              style={{ display: 'inline-block', width: '50%', paddingRight: '8px' }}
            >
              <Select options={firstLevelOptions} placeholder='一级分类' />
            </Form.Item>
            <Form.Item
              name='dataSubType'
              className={styles.formItem}
              style={{ display: 'inline-block', width: '50%', paddingLeft: '8px' }}
            >
              <Select options={secondLevelOptions} placeholder='二级分类' />
            </Form.Item>
          </div>

          <Form.Item label='数据分级：' name='dataLevel' className={styles.formItem}>
            <Radio.Group>
              <Radio value='public'>公开</Radio>
              <Radio value='specificOrg'>指定机构可见</Radio>
              <Radio value='privateA'>私密A</Radio>
              <Radio value='privateB'>私密B</Radio>
            </Radio.Group>
          </Form.Item>

          <div className={styles.formRow}>
            <Form.Item
              label='计费方式：'
              name='billingMethod'
              className={styles.formItem}
              style={{ display: 'inline-block', width: '50%', paddingRight: '8px' }}
            >
              <Input defaultValue='使用次数' disabled />
            </Form.Item>
            <Form.Item
              label='参考价格：'
              name='referencePrice'
              className={styles.formItem}
              style={{ display: 'inline-block', width: '50%', paddingLeft: '8px' }}
              extra={<span className={styles.charCount}>元/次</span>}
            >
              <InputNumber min={0} defaultValue={0.1} style={{ width: '100%' }} />
            </Form.Item>
          </div>

          <Form.Item label='数据交易机构：' name='tradingInstitution' className={styles.formItem}>
            <Select defaultValue='dataInstitution'>
              <Select.Option value='dataInstitution'>可信数据空间&数据交易所</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item label='支付方式：' name='paymentMethod' className={styles.formItem}>
            <Input defaultValue='隐私计算' disabled />
          </Form.Item>

          <Form.Item label='挂接数据源：' name='dataSource' className={styles.formItem}>
            <Upload {...uploadProps} listType='picture-card' className={styles.uploader}>
              {uploadButton}
            </Upload>
          </Form.Item>

          <Form.Item label='合规评估报告：' name='complianceReport' className={styles.formItem}>
            <Upload {...uploadProps} listType='picture-card' className={styles.uploader}>
              {uploadButton}
            </Upload>
          </Form.Item>

          <Form.Item label='质量评估报告：' name='qualityReport' className={styles.formItem}>
            <Upload {...uploadProps} listType='picture-card' className={styles.uploader}>
              {uploadButton}
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  )
}

export default ResourceTable
