import type { LoginParams, UserInfo } from '@/types'
import { type FC, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Form, Input, Checkbox, Button, message } from 'antd'
import { useAppSelector, useAppDispatch } from '@/stores'
import { setToken, setUserInfo, setSessionTimeout } from '@/stores/modules/user'
import { getAuthCache } from '@/utils/auth'
import { TOKEN_KEY } from '@/enums/cacheEnum'
import { loginApi, getUserInfo, getVerifyCodeApi } from '@/api'
import styles from './index.module.less'

const LoginPage: FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [codeSending, setCodeSending] = useState(false)
  const [countdown, setCountdown] = useState(0)

  const dispatch = useAppDispatch()

  const { token, sessionTimeout } = useAppSelector(state => state.user)
  const getToken = (): string => {
    return token || getAuthCache<string>(TOKEN_KEY)
  }

  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  const handleLogin = async (values: any) => {
    try {
      setLoading(true)
      const userInfo = await loginAction({
        username: values.username,
        verifyCode: values.verifyCode
      })
      if (userInfo) {
        message.success('登录成功！')
      }
    } catch (error) {
      // message已在axios拦截器中显示，这里不需要再显示
    } finally {
      setLoading(false)
    }
  }

  const loginAction = async (
    params: LoginParams & {
      goHome?: boolean
    }
  ): Promise<UserInfo | null> => {
    try {
      const { goHome = true, ...loginParams } = params
      const data = await loginApi(loginParams)

      // 保存 Token
      dispatch(setToken(data?.token))
      return afterLoginAction(goHome)
    } catch (error) {
      return Promise.reject(error)
    }
  }

  const afterLoginAction = async (goHome?: boolean): Promise<UserInfo | null> => {
    if (!getToken()) return null

    const userInfo = await getUserInfoAction()

    if (sessionTimeout) {
      dispatch(setSessionTimeout(false))
    } else {
      const redirect = searchParams.get('redirect')
      if (redirect) {
        navigate(redirect)
      } else if (goHome) {
        navigate(userInfo?.homePath || '/home')
      }
    }

    return userInfo
  }

  const getUserInfoAction = async (): Promise<UserInfo | null> => {
    if (!getToken()) return null

    const userInfo = await getUserInfo()

    dispatch(setUserInfo(userInfo))

    return userInfo
  }

  const handleRegister = () => {
    navigate('/register')
  }

  const handleReset = () => {
    navigate('/reset')
  }

  // 手机号码验证规则
  const validatePhone = (_: any, value: string) => {
    const phoneRegex = /^1[3-9]\d{9}$/
    if (!value) {
      return Promise.reject('请输入手机号码')
    }
    if (!phoneRegex.test(value)) {
      return Promise.reject('请输入正确的手机号码格式')
    }
    return Promise.resolve()
  }

  // 发送验证码
  const sendVerifyCode = async () => {
    try {
      // 验证手机号
      const phone = form.getFieldValue('username')
      await form.validateFields(['username'])

      setCodeSending(true)
      await getVerifyCodeApi(phone)

      // 倒计时
      setCountdown(60)
      const timer = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    } catch (error) {
      // 错误消息已由axios拦截器处理
    } finally {
      setCodeSending(false)
    }
  }

  return (
    <div className={styles['login-wrapper']}>
      <div className={styles['login-box']}>
        <div className={styles['login-left']}></div>
        <div className={styles['login-right']}>
          <div className={styles['login-title-left']}>登录</div>
          <Form
            form={form}
            initialValues={{
              username: '',
              verifyCode: '',
              agreement: true
            }}
            className={styles['login-box-form']}
            onFinish={handleLogin}
          >
            <Form.Item name='username' rules={[{ validator: validatePhone }]}>
              <Input placeholder='请输入手机号码' maxLength={11} />
            </Form.Item>
            <Form.Item name='verifyCode' rules={[{ required: true, message: '请输入验证码' }]}>
              <Input
                placeholder='请输入验证码'
                maxLength={6}
                style={{ height: '44px' }}
                suffix={
                  <Button
                    type='link'
                    style={{
                      padding: 0,
                      height: '100%',
                      color: '#2b6aff',
                      display: 'flex',
                      alignItems: 'center',
                      marginRight: '-7px'
                    }}
                    onClick={sendVerifyCode}
                    disabled={countdown > 0 || codeSending}
                  >
                    {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
                  </Button>
                }
              />
            </Form.Item>
            <Form.Item
              name='agreement'
              valuePropName='checked'
              rules={[{ required: true, message: '请阅读并同意协议' }]}
            >
              <Checkbox>
                我已阅读并同意<a>《用户服务协议》</a>和<a>《隐私协议》</a>
              </Checkbox>
            </Form.Item>
            <Form.Item style={{ marginBottom: 10 }}>
              <Button type='primary' htmlType='submit' className={styles['login-btn']} loading={loading}>
                登 录
              </Button>
            </Form.Item>
            <Form.Item style={{ marginBottom: 10 }}>
              <Button type='default' className={styles['register-btn']} onClick={handleRegister}>
                注 册
              </Button>
            </Form.Item>
            <div style={{ textAlign: 'right' }}>
              <a style={{ fontSize: '12px' }} onClick={handleReset}>
                重置密码
              </a>
            </div>
          </Form>
        </div>
      </div>
    </div>
  )
}

export default LoginPage
