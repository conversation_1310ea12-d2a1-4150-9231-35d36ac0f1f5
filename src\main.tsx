import React from 'react'
import ReactDOM from 'react-dom/client'
import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import { store, persistor } from './stores'
import App from './App'
import '@/design/index.less'
import './index.less'
// register svg icon
import 'virtual:svg-icons-register'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/lib/locale/zh_CN'
// 导入dayjs及其中文语言包
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'

// 设置dayjs语言为中文
dayjs.locale('zh-cn')

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <ConfigProvider locale={zhCN}>
    <React.StrictMode>
      <Provider store={store}>
        <PersistGate persistor={persistor}>
          <App />
        </PersistGate>
      </Provider>
    </React.StrictMode>
  </ConfigProvider>
)
