import React, { useState, useEffect } from 'react'
import { Card, Descriptions, Button, Tag, Spin, Tabs, Table, Divider } from 'antd'
import { useParams, useNavigate } from 'react-router-dom'
import type { TabsProps } from 'antd'

const OrderDeliveryDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [orderData, setOrderData] = useState<any>(null)

  useEffect(() => {
    // 模拟API请求获取订单详情
    setLoading(true)
    setTimeout(() => {
      setOrderData({
        id: id,
        orderName: '用户订单统计分析',
        orderNo: 'SZDE0MEOZITM03D64001',
        startDate: '2020-01-01',
        endDate: '2020-01-01',
        status: '已完成',
        paymentStatus: '已支付',
        paymentMethod: '在线支付',
        paymentTime: '2020-01-01 09:30:45',
        paymentAmount: 899,
        createTime: '2019-12-25 15:20:30',
        remark: '用于分析用户订单数据统计报表'
      })
      setLoading(false)
    }, 1000)
  }, [id])

  const getStatusTag = (status: string) => {
    switch (status) {
      case '待支付':
        return <Tag color='orange'>{status}</Tag>
      case '已支付':
        return <Tag color='green'>{status}</Tag>
      case '已完成':
        return <Tag color='blue'>{status}</Tag>
      default:
        return <Tag>{status}</Tag>
    }
  }

  // 交易记录数据
  const transactionRecords = [
    {
      key: '1',
      date: '2020-01-01 09:30:45',
      type: '支付订单',
      amount: '¥899.00',
      status: '成功',
      operator: 'admin'
    }
  ]

  // 操作日志数据
  const operationLogs = [
    {
      key: '1',
      date: '2020-01-01 09:30:45',
      operation: '订单支付',
      operator: 'admin',
      details: '用户完成订单支付'
    },
    {
      key: '2',
      date: '2019-12-25 15:20:30',
      operation: '创建订单',
      operator: 'admin',
      details: '用户创建订单'
    }
  ]

  // 表格列定义
  const transactionColumns = [
    {
      title: '交易时间',
      dataIndex: 'date',
      key: 'date'
    },
    {
      title: '交易类型',
      dataIndex: 'type',
      key: 'type'
    },
    {
      title: '交易金额',
      dataIndex: 'amount',
      key: 'amount'
    },
    {
      title: '交易状态',
      dataIndex: 'status',
      key: 'status',
      render: (text: string) => (text === '成功' ? <Tag color='green'>{text}</Tag> : <Tag color='red'>{text}</Tag>)
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator'
    }
  ]

  const logColumns = [
    {
      title: '操作时间',
      dataIndex: 'date',
      key: 'date'
    },
    {
      title: '操作类型',
      dataIndex: 'operation',
      key: 'operation'
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator'
    },
    {
      title: '操作详情',
      dataIndex: 'details',
      key: 'details'
    }
  ]

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '订单信息',
      children: orderData && (
        <Descriptions bordered column={2}>
          <Descriptions.Item label='订单名称'>{orderData.orderName}</Descriptions.Item>
          <Descriptions.Item label='订单编号'>{orderData.orderNo}</Descriptions.Item>
          <Descriptions.Item label='订单状态'>{getStatusTag(orderData.status)}</Descriptions.Item>
          <Descriptions.Item label='支付状态'>{getStatusTag(orderData.paymentStatus)}</Descriptions.Item>
          <Descriptions.Item label='开始日期'>{orderData.startDate}</Descriptions.Item>
          <Descriptions.Item label='结束日期'>{orderData.endDate}</Descriptions.Item>
          <Descriptions.Item label='支付方式'>{orderData.paymentMethod}</Descriptions.Item>
          <Descriptions.Item label='支付时间'>{orderData.paymentTime}</Descriptions.Item>
          <Descriptions.Item label='订单金额'>¥{orderData.paymentAmount.toFixed(2)}</Descriptions.Item>
          <Descriptions.Item label='创建时间'>{orderData.createTime}</Descriptions.Item>
          <Descriptions.Item label='备注' span={2}>
            {orderData.remark || '无'}
          </Descriptions.Item>
        </Descriptions>
      )
    },
    {
      key: '2',
      label: '交易记录',
      children: <Table columns={transactionColumns} dataSource={transactionRecords} pagination={false} />
    },
    {
      key: '3',
      label: '操作日志',
      children: <Table columns={logColumns} dataSource={operationLogs} pagination={false} />
    }
  ]

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size='large' />
      </div>
    )
  }

  return (
    <div style={{ padding: '20px' }}>
      <Card
        title='订单详情'
        extra={
          <Button type='primary' onClick={() => navigate('/order-delivery')}>
            返回列表
          </Button>
        }
      >
        <Tabs defaultActiveKey='1' items={items} />
      </Card>
    </div>
  )
}

export default OrderDeliveryDetail
