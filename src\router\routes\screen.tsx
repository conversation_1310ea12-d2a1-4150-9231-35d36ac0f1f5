import { lazy } from '@loadable/component'
import type { RouteObject } from '../types'
import { LazyLoad } from '@/components/LazyLoad'
import { LargeScreen } from '@/components/LargeScreen'

// Screen route - standalone page without layout
const ScreenRoute: RouteObject = {
  path: '/screen',
  name: 'Screen',
  element: (
    <LargeScreen width={1920} height={1080}>
      {LazyLoad(lazy(() => import('@/views/screen')))}
    </LargeScreen>
  ),
  meta: {
    title: '数交联大屏',
    icon: 'screen',
    orderNo: 13,
    hideMenu: false
  }
}

export default ScreenRoute
