import type { ReactNode } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { getAuthCache } from '@/utils/auth'
import { TOKEN_KEY } from '@/enums/cacheEnum'
import { useAppSelector } from '@/stores'

export const GuardRoute = ({ children }: { children: ReactNode }) => {
  // 注释掉登录验证，直接允许访问所有页面
  // const whiteList: string[] = ['/', '/home', '/login', '/screen']
  // const { pathname } = useLocation()
  // const { token } = useAppSelector(state => state.user)
  // const getToken = (): string => {
  //   return token || getAuthCache<string>(TOKEN_KEY)
  // }

  // if (!getToken()) {
  //   if (whiteList.includes(pathname)) {
  //     return <Navigate to='/login' replace />
  //   } else {
  //     return <Navigate to={`/login?redirect=${pathname}`} replace />
  //   }
  // }

  // 直接返回子组件，跳过登录验证
  return children
}
