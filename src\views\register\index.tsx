import type { Login<PERSON>ara<PERSON>, UserInfo } from '@/types'
import { type FC, useState, useEffect } from 'react'
import { useNavigate, useSearchParams, Link } from 'react-router-dom'
import { Form, Input, Checkbox, Button, message, Tabs } from 'antd'
import { UserOutlined, LockOutlined, MailOutlined, SafetyOutlined, MobileOutlined } from '@ant-design/icons'
import { useAppSelector, useAppDispatch } from '@/stores'
import { setToken, setUserInfo, setSessionTimeout } from '@/stores/modules/user'
import { getAuthCache } from '@/utils/auth'
import { TOKEN_KEY } from '@/enums/cacheEnum'
import { loginApi, getUserInfo } from '@/api'
import logoIcon from '@/assets/images/logo_name.png'
import classNames from 'classnames'
import styles from './index.module.less'

const RegisterPage: FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [countdown, setCountdown] = useState(0)

  const dispatch = useAppDispatch()

  const { token, sessionTimeout } = useAppSelector(state => state.user)
  const getToken = (): string => {
    return token || getAuthCache<string>(TOKEN_KEY)
  }

  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1)
      }, 1000)
    }
    return () => {
      if (timer) clearTimeout(timer)
    }
  }, [countdown])

  const handleRegister = async (values: any) => {
    try {
      setLoading(true)
      // Registration API call would go here
      console.log('Registration values:', values)
      message.success('注册成功！')
      navigate('/login')
    } catch (error) {
      message.error((error as unknown as Error).message)
    } finally {
      setLoading(false)
    }
  }

  const sendVerificationCode = () => {
    const mobile = form.getFieldValue('mobile')
    if (!mobile) {
      message.error('请输入手机号码')
      return
    }

    const mobilePattern = /^1[3-9]\d{9}$/
    if (!mobilePattern.test(mobile)) {
      message.error('请输入正确的手机号码')
      return
    }

    // Set countdown to 60 seconds
    setCountdown(60)
    message.success(`验证码已发送至 ${mobile}`)
  }

  return (
    <div className={styles.registerWrapper}>
      <div className={styles.registerContainer}>
        <div className={styles.registerLeft}>
          {/* CSS-based laptop illustration */}
          <div className={styles.laptopIllustration}></div>
        </div>
        <div className={styles.registerRight}>
          <div className={styles.registerHeader}>
            <div className={styles.registerTab}>注册</div>
            <Link to='/login' className={styles.loginLink}>
              已有账号，去登录
            </Link>
          </div>

          <Form form={form} className={styles.registerForm} onFinish={handleRegister}>
            <Form.Item name='username' rules={[{ required: true, message: '请输入员工姓名' }]}>
              <Input prefix={<UserOutlined className={styles.inputIcon} />} placeholder='请输入员工姓名' />
            </Form.Item>

            <Form.Item name='companyId' rules={[{ required: true, message: '请输入统一社会信用代码' }]}>
              <Input prefix={<LockOutlined className={styles.inputIcon} />} placeholder='请输入统一社会信用代码' />
            </Form.Item>

            <Form.Item
              name='mobile'
              rules={[
                { required: true, message: '请输入手机号码' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
              ]}
            >
              <Input prefix={<MobileOutlined className={styles.inputIcon} />} placeholder='请输入手机号码用于登录' />
            </Form.Item>

            <Form.Item name='verificationCode' rules={[{ required: true, message: '请输入验证码' }]}>
              <Input
                prefix={<MailOutlined className={styles.inputIcon} />}
                placeholder='请输入验证码'
                suffix={
                  <Button
                    className={styles.verificationCodeBtn}
                    onClick={sendVerificationCode}
                    type='link'
                    disabled={countdown > 0}
                  >
                    {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
                  </Button>
                }
              />
            </Form.Item>

            <Form.Item
              name='agreement'
              valuePropName='checked'
              rules={[
                { validator: (_, value) => (value ? Promise.resolve() : Promise.reject(new Error('请阅读并同意条款'))) }
              ]}
            >
              <Checkbox>
                我已阅读并同意<a href='#'>《用户服务协议》</a>和<a href='#'>《隐私政策》</a>
              </Checkbox>
            </Form.Item>

            <Form.Item>
              <Button type='primary' htmlType='submit' className={styles.registerBtn} loading={loading}>
                注 册
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  )
}

export default RegisterPage
