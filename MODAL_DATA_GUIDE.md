# 大屏弹框数据说明文档

## 概述
已为大屏数据列表中的九个弹框添加了完整的静态数据，每个弹框都包含丰富的演示数据。

## 九个弹框详细说明

### 1. 身份认证 (数字身份)
**数据字段**:
- `socialUnifiedCreditCode`: 统一社会信用代码
- `enterpriseName`: 企业名称
- `legalPerson`: 法人代表
- `registrationDate`: 注册日期
- `businessScope`: 经营范围

**示例数据**:
```json
{
  "socialUnifiedCreditCode": "91330100MA28A1234X",
  "enterpriseName": "杭州数据科技有限公司",
  "legalPerson": "张三",
  "registrationDate": "2023-01-15",
  "businessScope": "数据服务、技术开发"
}
```

### 2. 数据通证
**数据字段**:
- `socialUnifiedCreditCode`: 数据通证 NF Token
- `enterpriseName`: 持有机构名称
- `tokenId`: 通证ID
- `tokenName`: 通证名称

**示例数据**:
```json
{
  "socialUnifiedCreditCode": "TOKEN_001",
  "enterpriseName": "杭州数据科技有限公司",
  "tokenId": "TOKEN_001",
  "tokenName": "智慧城市数据通证"
}
```

### 3. 数据目录
**数据字段**:
- `dataRegisterNumber`: 登记编号
- `listedNumber`: 挂牌编号
- `dataFirstCategory`: 一级分类
- `dataSecondCategory`: 二级分类
- `dataAccessLevel`: 数据分级
- `transactionHash`: 链上哈希
- `dataExName`: 数据交易所

**示例数据**:
```json
{
  "dataRegisterNumber": "A0001111",
  "listedNumber": "L0001111",
  "dataFirstCategory": "公共数据",
  "dataSecondCategory": "城市管理",
  "dataAccessLevel": "公开",
  "transactionHash": "0x1234567890abcdef1234567890abcdef",
  "dataExName": "杭州数据交易所"
}
```

### 4. 登记挂牌
**包含两个证书**:
- 数据产权登记证书 (`registerVoucherData`)
- 数据产品挂牌证书 (`listedVoucherData`)

**数据字段**:
- `dataName`: 数据名称/挂牌编号
- `enterpriseName`: 登记主体
- `socialUnifiedCreditCode`: 统一社会信用代码
- `createTime/listedTime`: 首次登记时间
- `dataRegisterNumber`: 数据通证

### 5. 签约交易
**表格数据** (5条记录):
- 智慧城市交通数据
- 金融风控数据集
- 医疗健康数据
- 电商用户行为数据
- 物流配送数据

**数据字段**:
- `contractNumber`: 合同编号
- `dataName`: 数据名称
- `contractStartAt`: 合同开始时间
- `contractEndAt`: 合同结束时间
- `buyerEnterpriseName`: 需求方企业
- `sellerEnterpriseName`: 提供方企业

### 6. 合约计算订单
**表格数据** (4条记录):
- 智慧城市交通数据计算订单
- 金融风控数据计算订单
- 医疗健康数据计算订单
- 电商用户行为数据计算订单

**数据字段**:
- `contractNumber`: 订单编号
- `dataName`: 数据名称
- `contractStartAt`: 开始时间
- `contractEndAt`: 结束时间
- `taskCount`: 任务数量
- `orderStatus`: 订单状态
- `computeType`: 计算类型

### 7. 合约计算任务
**表格数据** (4条记录):
包含隐私求交、联邦学习、匿踪查询、多方安全计算等不同类型的任务

**数据字段**:
- `taskNumber`: 任务编号
- `taskName`: 任务名称
- `taskCompletionTime`: 完成时间
- `transactionHash`: 交易哈希
- `taskStatus`: 任务状态
- `computeType`: 计算类型
- `executionStatus`: 执行状态

### 8. 交付存证
**数据字段**:
- `orderCount`: 数据订单数量 (15个)
- `taskCount`: 数据任务数量 (8个)
- `stealthQueryCount`: 匿踪查询次数 (120次)
- `privacyCalculationCount`: 隐私求交次数 (85次)
- `executionDays`: 算力计量天数 (7天)

### 9. 计量计费
**表格数据** (4条记录):
包含不同订单的计费明细

**数据字段**:
- `contractNumber`: 订单编号
- `dataName`: 数据名称
- `anonymousQueryFee`: 数据匿踪查询计费
- `privacyIntersectionFee`: 数据隐私求交计费
- `privacyCalculationCount`: 订单算力计费
- `nodeCost`: 费用合计
- `paymentStatus`: 支付状态

### 10. 收益分配
**数据字段**:
- `buyerEnterpriseName`: 收益方
- `totalPrice`: 当前数据收益 (8500元)
- `nodeCost`: 当前算力收益 (1500元)
- 当前收益合计: 10000元

## 数据列表增强
主数据列表已扩展到9条记录，涵盖：
1. 智慧城市交通数据
2. 金融风控数据集
3. 医疗健康数据
4. 电商用户行为数据
5. 物流配送数据
6. 教育培训数据
7. 旅游景点客流数据
8. 能源消耗监测数据
9. 农业生产监测数据

## 使用说明
1. 点击大屏中的"数据登记总量"区域打开数据列表弹窗
2. 点击任意数据行打开数据详情弹窗
3. 在数据详情弹窗中点击九个功能按钮查看对应的详细信息
4. 所有数据都是静态模拟数据，用于演示展示

## 技术实现
- 所有数据存储在 `src/data/screenData.json` 文件中
- 通过本地JSON数据替代API请求
- 保持原有的交互逻辑和UI效果
- 支持搜索、分页、排序等功能

演示数据已完善，可以进行完整的功能展示！
