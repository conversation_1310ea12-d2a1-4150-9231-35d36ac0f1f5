import { lazy } from '@loadable/component'
import type { RouteObject } from '../types'
import { LayoutGuard } from '../guard'
import { LazyLoad } from '@/components/LazyLoad'

// Home route
const HomeRoute: RouteObject = {
  path: '/user',
  name: 'User',
  element: <LayoutGuard />,
  meta: {
    title: '用户管理',
    icon: 'user',
    orderNo: 11,
    hideChildrenInMenu: true
  },
  children: [
    {
      path: '',
      name: 'HomePage',
      element: LazyLoad(lazy(() => import('@/views/user'))),
      meta: {
        title: '用户管理',
        key: 'user',
        icon: 'user',
        orderNo: 11,
        hideMenu: true
      }
    }
  ]
}

export default HomeRoute
