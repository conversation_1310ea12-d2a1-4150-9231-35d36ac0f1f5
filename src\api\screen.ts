import { service } from '@/utils/axios'

export function getCount() {
  return service({
    url: '/data/count',
    method: 'get'
  })
}

// 数据登记趋势图
export function dataTrendChart() {
  return service({
    url: '/data/dataTrendChart',
    method: 'get'
  })
}
// 数据挂牌总量
export function dataListedCount() {
  return service({
    url: '/data/listedCount',
    method: 'get'
  })
}
// 数据签约数量
export function dataContractListedSignCount() {
  return service({
    url: '/dataContract/listedSignCount',
    method: 'get'
  })
}
// 交易所列表
export function dataExList() {
  return service({
    url: '/data/exchangeDataStatistics',
    method: 'get'
  })
}
// 数据交易统计
export function dataCalculatedCount() {
  return service({
    url: '/dataContractTask/dataCalculatedCount',
    method: 'get'
  })
}
// 数据列表
export function getDataList(data: any) {
  return service({
    url: '/dataContract/screenDataPage',
    method: 'post',
    data: data
  })
}
// 选择数据分类
export function listTreeCategory() {
  return service({
    url: '/dataIndustry/listTreeCategory',
    method: 'get'
  })
}
// 身份认证

export function identity(data: any) {
  return service({
    url: '/data/screenDataIdentity',
    method: 'get',
    params: data
  })
}

// 数据通证
export function screenDataToken(data: any) {
  return service({
    url: '/data/screenDataToken',
    method: 'get',
    params: data
  })
}

// 数据目录
export function screenDataDirectory(data: any) {
  return service({
    url: '/data/screenDataDirectory',
    method: 'get',
    params: data
  })
}
// 签约交易
export function screenDataSigning(data: any) {
  return service({
    url: '/dataContract/screenDataSigning',
    method: 'post',
    data: data
  })
}

// 合约计算订单

export function screenOrder(data: any) {
  return service({
    url: '/dataContract/screenOrder',
    method: 'post',
    data: data
  })
}
// 合约计算任务

export function screenTask(data: any) {
  return service({
    url: '/dataContractTask/screenTask',
    method: 'post',
    data: data
  })
}
// 任务内容
export function screenTaskContent(data: any) {
  return service({
    url: '/dataContractTask/screenTaskContent',
    method: 'get',
    params: data
  })
}

// 计费计量
export function measurementBilling(data: any) {
  return service({
    url: '/data/measurementBilling',
    method: 'post',
    data: data
  })
}
// 交付存证
export function screenDeliveryRecord(data: any) {
  return service({
    url: '/data/screenDeliveryRecord',
    method: 'get',
    params: data
  })
}
// 收益分配
export function calculateDataPrice(data: any) {
  return service({
    url: '/data/calculateDataPrice',
    method: 'get',
    params: data
  })
}
// 登记挂牌
export function listedVoucher(data: any) {
  return service({
    url: '/data/listedVoucher',
    method: 'get',
    params: data
  })
}
export function registerVoucher(data: any) {
  return service({
    url: '/data/registerVoucher',
    method: 'get',
    params: data
  })
}
