import { type FC, useEffect, useRef } from 'react'
import * as echarts from 'echarts/core'
import { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components'
import { PieChart } from 'echarts/charts'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import 'echarts-gl'
import './HazardInspectionCard.less'

// 注册必要的组件
echarts.use([GridComponent, TooltipComponent, LegendComponent, TitleComponent, PieChart, LabelLayout, CanvasRenderer])

interface HazardInspectionCardProps {
  title?: string
  count: number
  compareRate?: number
  mainColor?: string
  showTitle?: boolean
}

const HazardInspectionCard: FC<HazardInspectionCardProps> = ({
  title = '',
  count,
  compareRate = 0,
  mainColor = '#00a7ff',
  showTitle = false
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  let chartInstance: echarts.ECharts | null = null

  // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
  function getParametricEquation(
    startRatio: number,
    endRatio: number,
    isSelected: boolean,
    isHovered: boolean,
    k: number
  ) {
    // 计算
    const midRatio = (startRatio + endRatio) / 2

    const startRadian = startRatio * Math.PI * 2
    const endRadian = endRatio * Math.PI * 2
    const midRadian = midRatio * Math.PI * 2

    // 如果只有一个扇形，则不实现选中效果。
    if (startRatio === 0 && endRatio === 1) {
      isSelected = false
    }

    // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
    k = typeof k !== 'undefined' ? k : 1 / 3

    // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
    const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
    const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

    // 计算高亮效果的放大比例（未高亮，则比例为 1）
    const hoverRate = isHovered ? 1.05 : 1.6

    // 返回曲面参数方程
    return {
      u: {
        min: -Math.PI,
        max: Math.PI * 3,
        step: Math.PI / 32
      },

      v: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20
      },

      x: function (u: number, v: number) {
        if (u < startRadian) {
          return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        if (u > endRadian) {
          return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
      },

      y: function (u: number, v: number) {
        if (u < startRadian) {
          return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        if (u > endRadian) {
          return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
      },

      z: function (u: number, v: number) {
        if (u < -Math.PI * 0.5) {
          return Math.sin(u)
        }
        if (u > Math.PI * 2.5) {
          return Math.sin(u)
        }
        return Math.sin(v) > 0 ? 1 : -1
      }
    }
  }

  // 生成模拟 3D 饼图的配置项
  function getPie3D(pieData: any[], internalDiameterRatio?: number) {
    const series: any[] = []
    let sumValue = 0
    let startValue = 0
    let endValue = 0
    const legendData: string[] = []
    const k =
      typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3

    // 为每一个饼图数据，生成一个 series-surface 配置
    for (let i = 0; i < pieData.length; i++) {
      sumValue += pieData[i].value

      const seriesItem = {
        name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
        type: 'surface',
        parametric: true,
        wireframe: {
          show: false
        },
        pieData: pieData[i],
        pieStatus: {
          selected: false,
          hovered: false,
          k: k
        },
        itemStyle: {}
      }

      if (typeof pieData[i].itemStyle != 'undefined') {
        const itemStyle: any = {}

        if (typeof pieData[i].itemStyle.color !== 'undefined') {
          itemStyle.color = pieData[i].itemStyle.color
        }

        if (typeof pieData[i].itemStyle.opacity !== 'undefined') {
          itemStyle.opacity = pieData[i].itemStyle.opacity
        }

        seriesItem.itemStyle = itemStyle
      }
      series.push(seriesItem)
    }

    // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
    for (let i = 0; i < series.length; i++) {
      endValue = startValue + series[i].pieData.value

      series[i].pieData.startRatio = startValue / sumValue
      series[i].pieData.endRatio = endValue / sumValue
      series[i].parametricEquation = getParametricEquation(
        series[i].pieData.startRatio,
        series[i].pieData.endRatio,
        true,
        false,
        0.08
      )

      startValue = endValue

      legendData.push(series[i].name)
    }

    const angle = 0 // 角度
    // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
    series.push(
      {
        type: 'custom',
        coordinateSystem: 'none',
        renderItem: (params: any, api: any) => {
          return {
            type: 'arc',
            shape: {
              cx: api.getWidth() / 2,
              cy: api.getHeight() / 0.8,
              r: Math.min(api.getWidth(), api.getHeight()) / 1.5,
              startAngle: ((360 + -angle) * Math.PI) / 180,
              endAngle: ((180 + -angle) * Math.PI) / 180
            },
            style: {
              fill: 'transparent',
              stroke: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                // 定义渐变
                { offset: 0, color: '#003366' }, // 从中心到外的颜色
                { offset: 0.5, color: '#00a7ff' }, // 终点颜色
                { offset: 1, color: '#003366' } // 终点颜色
              ]),
              lineWidth: 4
            },
            scale: [1, Math.cos((60 * Math.PI) / 180)],
            silent: true
          }
        },
        data: [0]
      },
      {
        type: 'custom',
        coordinateSystem: 'none',
        renderItem: (params: any, api: any) => {
          return {
            type: 'arc',
            shape: {
              cx: api.getWidth() / 2,
              cy: api.getHeight() / 0.87,
              r: Math.min(api.getWidth(), api.getHeight()) / 1.7,
              startAngle: ((360 + -angle) * Math.PI) / 180,
              endAngle: ((180 + -angle) * Math.PI) / 180
            },
            style: {
              fill: 'transparent',
              stroke: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                // 定义渐变
                { offset: 0, color: '#003366' }, // 从中心到外的颜色
                { offset: 0.5, color: '#00a7ff' }, // 终点颜色
                { offset: 1, color: '#003366' } // 终点颜色
              ]),
              lineWidth: 4
            },
            scale: [1, Math.cos((60 * Math.PI) / 180)],
            silent: true
          }
        },
        data: [0]
      }
    )

    // 准备待返回的配置项，把准备好的 legendData、series 传入。
    const option = {
      backgroundColor: 'transparent',
      legend: {
        show: false,
        data: legendData
      },
      title: {
        text: `${compareRate}%`,
        left: 'center',
        top: '15%',
        textStyle: {
          fontSize: 22,
          color: '#fff'
          // fontWeight: 'bold'
        }
      },
      xAxis3D: {
        min: -1,
        max: 1
      },
      yAxis3D: {
        min: -1,
        max: 1
      },
      zAxis3D: {
        min: -1,
        max: 1
      },
      grid3D: {
        show: false,
        boxHeight: 0.1,
        top: '-10%',
        viewControl: {
          alpha: 35,
          rotateSensitivity: 0,
          zoomSensitivity: 0,
          panSensitivity: 0
        }
      },
      series: series
    }
    return option
  }

  const initChart = () => {
    if (!chartRef.current) return

    // 生成数据
    const arr = []
    const n = Math.round(compareRate || 0) // 使用比率作为进度
    for (let a = 0; a < 80; a++) {
      const obj = {
        name: 'a',
        value: 1,
        itemStyle: {
          color: a < 80 - n ? '#092b45' : '#00a7ff'
        }
      }
      arr.push(obj)
    }

    // 传入数据生成 option
    const option = getPie3D(arr)

    // 初始化图表
    chartInstance = echarts.init(chartRef.current)
    chartInstance.setOption(option)

    // 窗口大小变化时，重新调整图表大小
    const handleResize = () => {
      chartInstance?.resize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }

  useEffect(() => {
    initChart()

    return () => {
      if (chartInstance) {
        chartInstance.dispose()
        window.removeEventListener('resize', () => {
          chartInstance?.resize()
        })
      }
    }
  }, [count, mainColor, compareRate])

  return (
    <div className='hazard-inspection-card'>
      <div className='card-content'>
        <div className='chart-wrapper'>
          <div ref={chartRef} className='chart-container'></div>
        </div>
        <div className='info-wrapper'>
          <div className='info-title'>{title}</div>
          <div className='info-value'>{count}</div>
        </div>
      </div>
    </div>
  )
}

export default HazardInspectionCard
