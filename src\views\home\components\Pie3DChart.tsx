import { type FC, useEffect, useRef, useMemo, useCallback } from 'react'
import { Card } from 'antd'
import * as echarts from 'echarts/core'
import { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from 'echarts/components'
import { Pie<PERSON><PERSON> } from 'echarts/charts'
import { LabelLayout } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import 'echarts-gl'
import bingImage from '@/assets/images/bing.png'

// 注册必要的组件
echarts.use([GridComponent, TooltipComponent, LegendComponent, TitleComponent, PieChart, LabelLayout, CanvasRenderer])

interface Pie3DChartProps {
  loading: boolean
  height?: number
  data: {
    name: string
    value: number
    itemStyle?: {
      color?: string
    }
  }[]
  value: number
}

interface PieItemData {
  name: string
  value: number
  itemStyle?: {
    color?: string
    opacity?: number
  }
  startRatio?: number
  endRatio?: number
}

interface SeriesItem {
  name: string
  type: string
  parametric: boolean
  wireframe: {
    show: boolean
  }
  pieData: PieItemData
  pieStatus: {
    selected: boolean
    hovered: boolean
    k: number
  }
  itemStyle?: {
    color?: string
    opacity?: number
  }
  parametricEquation?: any
}

const Pie3DChart: FC<Pie3DChartProps> = ({ loading, height = 200, data, value }) => {
  const chartRef = useRef<HTMLDivElement>(null)
  let chartInstance: echarts.ECharts | null = null

  function getParametricEquation(
    startRatio: number,
    endRatio: number,
    isSelected: boolean,
    isHovered: boolean,
    k: number,
    height: number
  ) {
    // 计算
    const midRatio = (startRatio + endRatio) / 2

    const startRadian = startRatio * Math.PI * 2
    const endRadian = endRatio * Math.PI * 2
    const midRadian = midRatio * Math.PI * 2

    // 如果只有一个扇形，则不实现选中效果。
    if (startRatio === 0 && endRatio === 1) {
      isSelected = false
    }

    // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
    k = typeof k !== 'undefined' ? k : 1 / 3

    // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
    const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
    const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

    // 计算高亮效果的放大比例（未高亮，则比例为 1）
    const hoverRate = isHovered ? 1.05 : 1

    // 返回曲面参数方程
    return {
      u: {
        min: -Math.PI,
        max: Math.PI * 3,
        step: Math.PI / 32
      },

      v: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20
      },

      x: function (u: number, v: number) {
        if (u < startRadian) {
          return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        if (u > endRadian) {
          return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
      },

      y: function (u: number, v: number) {
        if (u < startRadian) {
          return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        if (u > endRadian) {
          return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
      },

      z: function (u: number, v: number) {
        if (u < -Math.PI * 0.5) {
          return Math.sin(u)
        }
        if (u > Math.PI * 2.5) {
          return Math.sin(u)
        }
        return Math.sin(v) > 0 ? 2 * height : -1
      }
    }
  }

  // 生成模拟 3D 饼图的配置项
  function getPie3D(pieData: PieItemData[], internalDiameterRatio: number) {
    const series: any[] = []
    let sumValue = 0
    let startValue = 0
    let endValue = 0
    const legendData: string[] = []
    const k =
      typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3

    // 为每一个饼图数据，生成一个 series-surface 配置
    for (let i = 0; i < pieData.length; i++) {
      sumValue += pieData[i].value

      const seriesItem: SeriesItem = {
        name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
        type: 'surface',
        parametric: true,
        wireframe: {
          show: false
        },
        pieData: pieData[i],
        pieStatus: {
          selected: false,
          hovered: false,
          k: k
        }
      }

      if (typeof pieData[i].itemStyle != 'undefined') {
        seriesItem.itemStyle = {
          color: pieData[i].itemStyle?.color,
          opacity: pieData[i].itemStyle?.opacity
        }
      }
      series.push(seriesItem)
    }

    // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
    // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
    for (let i = 0; i < series.length; i++) {
      endValue = startValue + series[i].pieData.value
      series[i].pieData.startRatio = startValue / sumValue
      series[i].pieData.endRatio = endValue / sumValue
      series[i].parametricEquation = getParametricEquation(
        series[i].pieData.startRatio,
        series[i].pieData.endRatio,
        false,
        false,
        k,
        series[i].pieData.value
      )

      startValue = endValue
      legendData.push(series[i].name)
    }

    // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
    series.push({
      name: 'mouseoutSeries',
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false
      },
      itemStyle: {
        opacity: 0.1,
        color: '#f0f'
      },
      parametricEquation: {
        u: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20
        },
        v: {
          min: 0,
          max: Math.PI,
          step: Math.PI / 20
        },
        x: function (u: number, v: number) {
          return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2.1
        },
        y: function (u: number, v: number) {
          return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2.1
        },
        z: function (u: number, v: number) {
          return Math.cos(v) > 0 ? 0 : -5
        }
      }
    })

    series.push({
      name: 'mouseoutSeries',
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false
      },
      itemStyle: {
        opacity: 0.1,
        color: '#0ff'
      },
      parametricEquation: {
        u: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20
        },
        v: {
          min: 0,
          max: Math.PI,
          step: Math.PI / 20
        },
        x: function (u: number, v: number) {
          return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2.1
        },
        y: function (u: number, v: number) {
          return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2.1
        },
        z: function (u: number, v: number) {
          return Math.cos(v) > 0 ? -15 : -20
        }
      }
    })

    return series
  }

  const getPie3DOptions = useMemo(() => {
    const colors = ['#93DBFF', '#5AF3B8']
    const xData = ['已交易数量', '待交易数量']
    const yData = [data[0]?.value || 0, data[1]?.value || 0]

    // 传入数据生成 option
    const optionsData: PieItemData[] = []
    for (let i = 0; i < xData.length; i++) {
      optionsData.push({
        name: xData[i],
        value: yData[i] / 1, // 不除法，直接使用原始值
        itemStyle: {
          color: colors[i]
        }
      })
    }

    const series = getPie3D(optionsData, 0.8)

    // 添加原始数据用于tooltip显示
    const originalData = yData.map((value, index) => ({
      name: xData[index],
      value: value
    }))

    series.push({
      name: 'pie2d',
      type: 'pie',
      label: {
        opacity: 1,
        fontSize: 12,
        lineHeight: 20,
        textStyle: {
          fontSize: 12
        }
      },
      labelLine: {
        length: 60,
        length2: 60
      },
      startAngle: -50, //起始角度，支持范围[0, 360]。
      clockwise: false, //饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
      radius: ['0', '40%'],
      center: ['50%', '50%'],
      data: optionsData,
      itemStyle: {
        opacity: 0
      }
    })

    // 准备待返回的配置项，把准备好的 legendData、series 传入。
    const option = {
      //图例显示隐藏
      // legend: {
      //   tooltip: {
      //     show: true
      //   },
      //   data: xData,
      //   bottom: '5%',
      //   itemGap: 20,
      //   textStyle: {
      //     color: '#fff',
      //     fontSize: 12
      //   }
      // },
      animation: true,
      tooltip: {
        formatter: (params: any) => {
          if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
            const seriesIndex = series.findIndex((item: any) => item.name === params.seriesName)
            if (seriesIndex !== -1 && series[seriesIndex].pieData) {
              // 查找原始数据显示
              const originalIndex = originalData.findIndex(item => item.name === params.seriesName)
              const originalValue =
                originalIndex !== -1 ? originalData[originalIndex].value : series[seriesIndex].pieData.value * 10000
              return `${params.seriesName}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>${originalValue}`
            }
          }
          return ''
        },
        textStyle: {
          fontSize: 12
        }
      },
      title: {
        x: 'center',
        top: '20',
        textStyle: {
          color: '#fff',
          fontSize: 12
        }
      },
      backgroundColor: 'transparent',
      labelLine: {
        show: false,
        lineStyle: {
          color: 'transparent'
        }
      },
      label: {
        show: false,
        color: 'transparent',
        position: 'outside',
        formatter: '{b} \n{c} {d}%'
      },
      xAxis3D: {
        min: -1,
        max: 1
      },
      yAxis3D: {
        min: -1,
        max: 1
      },
      zAxis3D: {
        min: -1,
        max: 1
      },
      grid3D: {
        show: false,
        boxHeight: 0.5,
        top: '-10%',

        viewControl: {
          autoRotate: false, // 关闭自动旋转
          rotateSensitivity: 0, // 禁止鼠标旋转
          zoomSensitivity: 0, // 禁止鼠标缩放
          panSensitivity: 0, // 禁止鼠标平移
          rotateMouseButton: 'none', // 禁用鼠标旋转
          panMouseButton: 'none', // 禁用鼠标平移
          zoomMouseButton: 'none', // 禁用鼠标缩放
          alpha: 30, // 视角旋转角度
          beta: 30 // 视角倾斜角度
        }
      },
      series: series
    }

    return option
  }, [data])

  const initChart = useCallback(() => {
    const el = chartRef.current
    if (!el) return

    // 如果已存在图表实例，先销毁
    if (chartInstance) {
      chartInstance.dispose()
    }

    chartInstance = echarts.init(el)
    chartInstance.setOption(getPie3DOptions)

    const handleResize = () => {
      chartInstance?.resize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [getPie3DOptions])

  useEffect(() => {
    if (!loading) {
      const timer = setTimeout(() => {
        initChart()
      }, 300) // 增加延迟确保DOM已完全渲染

      return () => {
        clearTimeout(timer)
      }
    }
  }, [loading, initChart])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (chartInstance) {
        chartInstance.dispose()
      }
    }
  }, [])

  return (
    <Card
      loading={loading}
      bordered={false}
      title='3D参数化饼图'
      style={{
        background: 'transparent',
        borderRadius: '4px',
        color: '#fff',
        position: 'relative'
      }}
      headStyle={{ color: '#fff', borderBottom: '1px solid #444' }}
    >
      <div
        style={{
          position: 'absolute',
          bottom: '55px',
          left: 0,
          right: 0,
          height: '150px',
          backgroundImage: `url(${bingImage})`,
          backgroundSize: '130%',
          backgroundPosition: 'center bottom',
          backgroundRepeat: 'no-repeat',
          opacity: 0.8,
          zIndex: 0
        }}
      />
      <div
        ref={chartRef}
        style={{
          width: '100%',
          height: height + 'px',
          minHeight: '200px',
          position: 'relative',
          zIndex: 1
        }}
      />
    </Card>
  )
}

export default Pie3DChart
