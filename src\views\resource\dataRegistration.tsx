import type { ColumnsType } from 'antd/es/table'
import { type FC, useState, useEffect } from 'react'
import { type TableProps, Card, Button, Table, Tag, Input, Space, Form, Select } from 'antd'
import { getTableList } from '@/api'
import type { APIResult, PageState, DataRegistrationType } from './types'

const dataTypeOptions = [{ label: '企业数据', value: '企业数据' }]

const dataLevelOptions = [
  { label: '公开可见', value: '公开可见' },
  { label: '指定机构可见', value: '指定机构可见' }
]

const DataRegistration: FC = () => {
  const [tableLoading, setTableLoading] = useState(false)
  const [tableData, setTableData] = useState<DataRegistrationType[]>([])
  const [tableTotal, setTableTotal] = useState<number>(0)
  const [tableQuery, setTableQuery] = useState<PageState>({ current: 1, pageSize: 10 })

  const columns: ColumnsType<DataRegistrationType> = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      render: (_: any, __: any, idx: number) => idx + 1 + (tableQuery.current - 1) * tableQuery.pageSize
    },
    {
      title: '数据名称',
      dataIndex: 'dataName',
      align: 'center'
    },
    {
      title: '登记编号',
      dataIndex: 'registrationNumber',
      align: 'center'
    },
    {
      title: '登记主体',
      dataIndex: 'registrationEntity',
      align: 'center'
    },
    {
      title: '登记时间',
      dataIndex: 'registrationTime',
      align: 'center'
    },
    {
      title: '数据分类',
      dataIndex: 'dataType',
      align: 'center'
    },
    {
      title: '数据分级',
      dataIndex: 'dataLevel',
      align: 'center'
    }
    // {
    //   title: '登记凭证',
    //   dataIndex: 'registrationCertificate',
    //   align: 'center',
    //   render: (_: string) => <Button type='link'>查看详情</Button>
    // },
    // {
    //   title: '挂接状态',
    //   dataIndex: 'connectStatus',
    //   align: 'center',
    //   render: (value: boolean) => (
    //     <Tag color={value ? 'green' : 'default'} className='status-tag'>
    //       {value ? '在线' : '离线'}
    //     </Tag>
    //   )
    // }
  ]

  useEffect(() => {
    fetchData()
  }, [tableQuery])

  async function fetchData() {
    setTableLoading(true)
    try {
      const data = await getTableList(tableQuery)
      const { list, total } = data as unknown as APIResult
      setTableData(list)
      setTableTotal(total)
    } catch (error) {
      console.error('Failed to fetch data', error)
      // Mock data for development
      const mockData: DataRegistrationType[] = [
        {
          id: '1',
          dataName: '用户行为统计分析',
          registrationNumber: 'SZDEXRE021740326401',
          registrationEntity: '西昌海河交旅投资发展有限公司',
          registrationTime: '2022-01-01 15:43:33',
          dataType: '企业数据',
          dataLevel: '公开可见',
          registrationCertificate: '查看详情',
          connectStatus: true
        },
        {
          id: '2',
          dataName: '用户行为统计分析',
          registrationNumber: 'SZDEXRE021740326401',
          registrationEntity: '西昌海河交旅投资发展有限公司',
          registrationTime: '2022-01-01 15:43:33',
          dataType: '企业数据',
          dataLevel: '指定机构可见',
          registrationCertificate: '查看详情',
          connectStatus: false
        },
        {
          id: '3',
          dataName: '用户行为统计分析',
          registrationNumber: 'SZDEXRE021740326401',
          registrationEntity: '西昌海河交旅投资发展有限公司',
          registrationTime: '2022-01-01 15:43:33',
          dataType: '企业数据',
          dataLevel: '指定机构可见',
          registrationCertificate: '查看详情',
          connectStatus: true
        }
      ]
      setTableData(mockData)
      setTableTotal(mockData.length)
    }
    setTableLoading(false)
  }

  function handlePageChange(page: number, pageSize: number) {
    setTableQuery({ ...tableQuery, current: page, pageSize })
  }

  return (
    <Card bordered={false}>
      <Space style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <Form layout='inline' onFinish={values => setTableQuery({ ...tableQuery, ...values, current: 1 })}>
          <Form.Item name='search'>
            <Input placeholder='数据名称/登记编号' style={{ width: 200 }} />
          </Form.Item>
          <Form.Item name='dataType'>
            <Select placeholder='数据分类' options={dataTypeOptions} allowClear style={{ width: 120 }} />
          </Form.Item>
          <Form.Item name='dataLevel'>
            <Select placeholder='数据分级' options={dataLevelOptions} allowClear style={{ width: 120 }} />
          </Form.Item>
          <Form.Item>
            <Button type='primary' htmlType='submit'>
              查询
            </Button>
          </Form.Item>
          <Form.Item>
            <Button
              htmlType='button'
              onClick={() => {
                setTableQuery({ current: 1, pageSize: 10 })
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
        <Button type='primary'>资源登记</Button>
      </Space>
      <Table
        rowKey='id'
        columns={columns}
        dataSource={tableData}
        loading={tableLoading}
        pagination={{
          current: tableQuery.current,
          pageSize: tableQuery.pageSize,
          total: tableTotal,
          showTotal: total => `共 ${total} 条`,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          onChange: handlePageChange
        }}
      />
    </Card>
  )
}

export default DataRegistration
