/* 数据登记总量弹窗样式 */

.ant-modal-content {
  // background-color: #041138 !important;
  background: url('./assets/images/tan.png') no-repeat;
  background-size: 100% 100%;
  color: white;
  border-radius: 4px;
  height: 480px;
  margin-top: 6%;
}

.ant-modal-header {
  background-color: transparent !important;
  border-bottom: 1px solid #041138;
}

.ant-modal-title {
  color: white !important;
  font-size: 20px !important;
  background-color: transparent !important;
}

.ant-modal-close-x {
  color: white;
  position: relative;
  font-size: 30px;
  top: -10px;
}

.ant-table-container table {
  background-color: transparent !important;
}

.ant-table-thead > tr > th {
  background-color: rgba(24, 144, 255, 0.2) !important;
  color: white !important;
  border-bottom: 1px solid #041138 !important;
}
.ant-table-tbody > tr {
  background-color: #061a49 !important;
}

.ant-table-tbody > tr > td {
  background-color: #061a49 !important;
  border-bottom: 1px solid #1a60a5 !important;
  color: white !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: rgba(24, 144, 255, 0.1) !important;
}

.ant-pagination-item-active {
  border-color: #041138 !important;
  background-color: rgba(24, 144, 255, 0.2) !important;
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  color: #858c9f !important;
}

.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
  color: #858c9f !important;
}

.ant-pagination-options-quick-jumper {
  color: #858c9f !important;
}

.ant-pagination-options-quick-jumper input {
  background-color: #00103f !important;
  border: 1px solid #00103f !important;
  color: #858c9f;
}

.ant-select-selector {
  background-color: #00103f !important;
  border: 1px solid #00103f !important;
  color: #858c9f !important;
}

.ant-select-selection-item {
  color: #858c9f !important;
}

/* Input placeholder 样式 */
.ant-input::placeholder {
  color: #858c9f !important;
  opacity: 0.7;
}

/* DatePicker 样式 */
.ant-picker {
  background-color: #00103f !important;
  border: 1px solid #00103f !important;
}

.ant-picker-input > input {
  color: #858c9f !important;
}

.ant-picker-input > input::placeholder {
  color: #858c9f !important;
  opacity: 0.7;
}

.ant-picker-suffix {
  color: #858c9f !important;
}

.ant-picker-clear {
  background-color: #00103f !important;
  color: #858c9f !important;
}

.ant-picker-range-separator {
  color: #858c9f !important;
}

:where(.css-dev-only-do-not-override-36gkoj).ant-picker-outlined {
  border-color: #00103f !important;
}

:where(.css-dev-only-do-not-override-36gkoj).ant-input-outlined:hover {
  background-color: #00103f !important;
  border: 1px solid #00103f !important;
  color: #858c9f !important;
}

.ant-btn-primary {
  background-color: #3563e9;
  border-color: #3563e9;
  color: #becbe9;
}

:where(.css-dev-only-do-not-override-1m63z2v).ant-btn-variant-solid:not(:disabled):not(.ant-btn-disabled):hover {
  background-color: #3563e9;
  border-color: #3563e9;
  color: #858c9f;
}

.reset-btn {
  background-color: #041138;
  border-color: #041138;
  color: #858c9f;
}
:where(.css-dev-only-do-not-override-36gkoj).ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover,
:where(.css-dev-only-do-not-override-36gkoj).ant-btn-variant-dashed:not(:disabled):not(.ant-btn-disabled):hover {
  background-color: #041138;
  border-color: #041138;
  color: #858c9f;
}
:where(.css-dev-only-do-not-override-1m63z2v).ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover,
:where(.css-dev-only-do-not-override-1m63z2v).ant-btn-variant-dashed:not(:disabled):not(.ant-btn-disabled):hover {
  background-color: #041138;
  border-color: #041138;
  color: #858c9f;
}

.dataModalHeader {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
}

.filterItem {
  display: flex;
  align-items: center;
  margin-right: 15px;

  span {
    margin-right: 8px;
    color: 858c9f;
  }

  input {
    background-color: #00103f !important;
    border: 1px solid #00103f !important;
    color: 858c9f;
    padding: 4px 8px;
    width: 160px;
  }
}

/* 强制覆盖表格背景色 */
.dataModal {
  color: #1890ff;
  .ant-table {
    background: transparent !important;
  }

  .ant-table-wrapper {
    background: transparent !important;
  }

  .ant-table-container {
    background: transparent !important;
  }

  .ant-table-content {
    background: transparent !important;
  }
}

:where(.css-dev-only-do-not-override-1m63z2v).ant-table-wrapper
  .ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before,
:where(.css-dev-only-do-not-override-1m63z2v).ant-table-wrapper
  .ant-table-thead
  > tr
  > td:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
  background: transparent !important;
}

.ant-select .ant-select-arrow {
  color: #007eff !important;
}

:where(.css-dev-only-do-not-override-36gkoj).ant-table-wrapper
  .ant-table-thead
  > tr
  > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before,
:where(.css-dev-only-do-not-override-36gkoj).ant-table-wrapper
  .ant-table-thead
  > tr
  > td:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
  background: #0f2d55 !important;
}
.ant-tooltip .ant-tooltip-inner {
  background: #1965b2 !important;
}
:where(.css-dev-only-do-not-override-1m63z2v).ant-pagination .ant-pagination-item a:hover {
  color: #007eff !important;
}
