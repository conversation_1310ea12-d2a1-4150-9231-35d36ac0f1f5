.status-tag {
  padding: 3px 10px;
  border-radius: 12px;
  font-weight: 500;
}

.formRow {
  position: relative;
  margin-bottom: 16px;
  width: 100%;
}

.registrationForm {
  padding: 0 20px;
}

.formItem {
  margin-bottom: 24px;
}

.checkboxGroup {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.uploader {
  width: 100%;
}

.uploader :global(.ant-upload.ant-upload-select-picture-card) {
  width: 100%;
  margin-right: 0;
  margin-bottom: 0;
}

.charCount {
  position: absolute;
  right: 0;
  color: #999;
  font-size: 12px;
} 