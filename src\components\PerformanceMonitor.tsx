import { useEffect, useRef, useState } from 'react'
import { message } from 'antd'

interface PerformanceMetrics {
  renderTime: number
  apiResponseTime: number
  memoryUsage: number
  fps: number
}

interface PerformanceMonitorProps {
  enabled?: boolean
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void
}

/**
 * 性能监控组件，用于后台监控页面性能指标（不显示UI）
 */
export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  enabled = process.env.NODE_ENV === 'development',
  onMetricsUpdate
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    apiResponseTime: 0,
    memoryUsage: 0,
    fps: 0
  })

  const frameCountRef = useRef(0)
  const lastTimeRef = useRef(performance.now())
  const renderStartTimeRef = useRef(0)

  // 监控FPS
  useEffect(() => {
    if (!enabled) return

    let animationId: number

    const measureFPS = () => {
      frameCountRef.current++
      const currentTime = performance.now()

      if (currentTime - lastTimeRef.current >= 1000) {
        const fps = Math.round((frameCountRef.current * 1000) / (currentTime - lastTimeRef.current))

        setMetrics(prev => {
          const newMetrics = { ...prev, fps }
          onMetricsUpdate?.(newMetrics)
          return newMetrics
        })

        frameCountRef.current = 0
        lastTimeRef.current = currentTime
      }

      animationId = requestAnimationFrame(measureFPS)
    }

    animationId = requestAnimationFrame(measureFPS)

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
    }
  }, [enabled, onMetricsUpdate])

  // 监控内存使用
  useEffect(() => {
    if (!enabled) return

    const measureMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        const memoryUsage = Math.round(memory.usedJSHeapSize / 1024 / 1024) // MB

        setMetrics(prev => {
          const newMetrics = { ...prev, memoryUsage }
          onMetricsUpdate?.(newMetrics)
          return newMetrics
        })
      }
    }

    const interval = setInterval(measureMemory, 5000) // 每5秒检查一次内存

    return () => clearInterval(interval)
  }, [enabled, onMetricsUpdate])

  // 监控渲染时间
  useEffect(() => {
    if (!enabled) return

    renderStartTimeRef.current = performance.now()

    return () => {
      const renderTime = performance.now() - renderStartTimeRef.current

      setMetrics(prev => {
        const newMetrics = { ...prev, renderTime }
        onMetricsUpdate?.(newMetrics)
        return newMetrics
      })
    }
  })

  // 性能警告
  useEffect(() => {
    if (!enabled) return

    const { fps, memoryUsage, renderTime } = metrics

    // FPS过低警告
    if (fps > 0 && fps < 30) {
      console.warn(`性能警告: FPS过低 (${fps})`)
    }

    // 内存使用过高警告
    if (memoryUsage > 100) {
      // 100MB
      console.warn(`性能警告: 内存使用过高 (${memoryUsage}MB)`)
    }

    // 渲染时间过长警告
    if (renderTime > 100) {
      // 100ms
      console.warn(`性能警告: 渲染时间过长 (${renderTime.toFixed(2)}ms)`)
    }
  }, [enabled, metrics])

  // 性能监控组件不显示UI，只在后台监控
  return null
}

/**
 * API性能监控Hook
 */
export const useApiPerformanceMonitor = () => {
  const measureApiCall = async <T,>(apiCall: () => Promise<T>, apiName: string): Promise<T> => {
    const startTime = performance.now()

    try {
      const result = await apiCall()
      const endTime = performance.now()
      const responseTime = endTime - startTime

      // 记录API响应时间
      console.log(`API ${apiName} 响应时间: ${responseTime.toFixed(2)}ms`)

      // 响应时间过长警告
      if (responseTime > 3000) {
        // 3秒
        console.warn(`API性能警告: ${apiName} 响应时间过长 (${responseTime.toFixed(2)}ms)`)
        message.warning(`${apiName} 响应较慢，请检查网络连接`)
      }

      return result
    } catch (error) {
      const endTime = performance.now()
      const responseTime = endTime - startTime
      console.error(`API ${apiName} 失败，耗时: ${responseTime.toFixed(2)}ms`, error)
      throw error
    }
  }

  return { measureApiCall }
}

/**
 * 组件渲染性能监控Hook
 */
export const useRenderPerformanceMonitor = (componentName: string) => {
  const renderStartTime = useRef(performance.now())
  const renderCount = useRef(0)

  useEffect(() => {
    renderCount.current++
    const renderTime = performance.now() - renderStartTime.current

    console.log(`${componentName} 渲染 #${renderCount.current}: ${renderTime.toFixed(2)}ms`)

    if (renderTime > 50) {
      // 50ms
      console.warn(`渲染性能警告: ${componentName} 渲染时间过长 (${renderTime.toFixed(2)}ms)`)
    }

    renderStartTime.current = performance.now()
  })

  return {
    renderCount: renderCount.current
  }
}

export default PerformanceMonitor
