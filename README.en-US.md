<p align="center">
  <img width="420" src="https://cdn.jsdelivr.net/gh/baimingxuan/media-store/images/logo-r-md.png">
</p>
<p align="center">
  <a href="https://github.com/facebook/react">
    <img src="https://img.shields.io/badge/react-18.2.0-brightgreen.svg" alt="react">
  </a>
  <a href="https://github.com/remix-run/react-router">
    <img src="https://img.shields.io/badge/react_router-6.19.0-brightgreen.svg" alt="react-router">
  </a>
  <a href="https://github.com/reduxjs/redux-toolkit">
    <img src="https://img.shields.io/badge/react_redux-8.1.3-brightgreen.svg" alt="redux-toolkit">
  </a>
  <a href="https://github.com/vitejs/vite">
    <img src="https://img.shields.io/badge/vite-4.5.0-brightgreen.svg" alt="vite">
  </a>
  <a href="https://github.com/ant-design/ant-design">
    <img src="https://img.shields.io/badge/antd-5.11.2-brightgreen.svg" alt="antd">
  </a>
 <a href="https://github.com/microsoft/TypeScript">
    <img src="https://img.shields.io/badge/typescript-5.2.2-brightgreen.svg" alt="typescript">
  </a>
  <a href="https://github.com/less">
    <img src="https://img.shields.io/badge/less-4.2.0-brightgreen.svg" alt="less">
  </a>
</p>

**English** | [中文](./README.md)

## Introduction

#### React-admin-design is a back-end management system solution based on React18 + ant-design5, it uses the latest front-end technology stack, and provides a wealth of functional components modules, can help you quickly build enterprise-level back-end front-end architecture.

## Feature

- **The Technology Stack**：Use React18, Vite4, TSX and other front-end technology development
- **Theming**: Configurable themes
- **International**：Built-in complete internationalization program
- **Mock Server** Built-in mock data scheme

## Preview

> ##### Preview Site：[https://baimingxuan.github.io/react-admin-design/](https://baimingxuan.github.io/react-admin-design/)
>
> ##### Github Site：[https://github.com/baimingxuan/react-admin-design](https://github.com/baimingxuan/react-admin-design)
>

![](https://cdn.jsdelivr.net/gh/baimingxuan/media-store/images/home-vue3.png)

## Development

```bash
// clone
git clone https://github.com/baimingxuan/react-admin-design.git

// install
pnpm install

// dev
pnpm run dev
```

## Build

```bash
// build
pnpm run build
```

## Browser support

The `Chrome 80+` browser is recommended for local development

Support modern browsers, not IE

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>IE | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt=" Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari |
| :----------------------------------------------------------: | :----------------------------------------------------------: | :----------------------------------------------------------: | :----------------------------------------------------------: | :----------------------------------------------------------: |
|                         not support                          |                       last 2 versions                        |                       last 2 versions                        |                       last 2 versions                        |                       last 2 versions                        |

## Git Contribution submission specification

- `feat` Add new features
- `fix` Fix the problem/BUG
- `style` The code style is related and does not affect the running result
- `perf` Optimization/performance improvement
- `refactor` Refactor
- `revert` Undo edit
- `test` Test related
- `docs` Documentation/notes
- `chore` Dependency update/scaffolding configuration modification etc.