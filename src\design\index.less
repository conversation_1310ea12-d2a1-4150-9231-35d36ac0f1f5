@import './antd.less';
@import './variable/index.less';
@import './public.less';
@import './scroll-bar.less';

// 自定义字体定义
@font-face {
  font-family: 'youshebiaotihei';
  src: url('/youshebiaotihei.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

// CSS resets
* {
  padding: 0;
  margin: 0;
}

ul,
li {
  list-style: none;
}

p {
  margin: 0 !important;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  outline: none;
  text-decoration: none;
}

html {
  height: 100%;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  box-sizing: border-box;

  &.color-weak {
    filter: invert(80%);
  }

  &.gray-mode {
    filter: grayscale(100%);
    filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
  }
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family:
    Microsoft YaHei,
    Helvetica Neue,
    Helvetica,
    PingFang SC,
    Hiragino Sans GB,
    Arial,
    sans-serif;
  font-size: 14px;
  color: #222;
}

img {
  width: 100%;
  height: 100%;
  max-width: none;
  border-style: none;
}

pre {
  font-family: Consolas, Menlo, Courier, monospace;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.clear-fix {
  &:before,
  &:after {
    content: '';
    display: table;
    clear: both;
  }
}

.flex-center-v {
  display: flex;
  align-items: center;
}

.flex-center-h {
  display: flex;
  justify-content: center;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between-h {
  display: flex;
  justify-content: space-between;
}
