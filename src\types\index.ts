export interface LoginParams {
  username: string // username现在用于存储手机号
  password?: string // 原密码字段，可选
  verifyCode?: string // 新增验证码字段，可选
}

export interface RegisterParams {
  mobile: string
  verifyCode: string
  orgName: string
  creditCode: string
}

export interface UserInfo {
  userId: string | number
  username: string
  realName: string
  avatar: string
  token: string
  desc?: string
  homePath?: string
}

export type ThemeMode = 'dark' | 'light'

export type LocaleType = 'zh_CN' | 'en'

export interface styleState {
  fontFamily?: string
  fontSize?: string
  lineHeight?: string
  color?: string
  backgroundColor?: string
  fontWeight?: string
  fontStyle?: string
  textShadow?: string
  textAlign?: string
}
