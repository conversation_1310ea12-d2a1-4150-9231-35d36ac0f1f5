{"name": "react-admin-design", "version": "1.0.0", "type": "module", "scripts": {"serve": "vite", "build": "rimraf dist &&  vite build", "preview": "vite preview", "clean:cache": "rimraf .eslintcache && rimraf node_modules && pnpm install", "lint:eslint": "eslint --cache \"{src,mock,build}/**/*.{js,ts,tsx}\" --fix", "lint:prettier": "prettier --write \"src/**/*.{js,json,ts,tsx,css,less,html,md}\"", "lint:lint-staged": "lint-staged", "prepare": "husky install"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@codemirror/lang-javascript": "^6.2.1", "@codemirror/view": "^6.22.0", "@loadable/component": "^5.16.3", "@logicflow/core": "^1.2.18", "@logicflow/extension": "^1.2.19", "@reduxjs/toolkit": "^2.0.1", "@uiw/react-codemirror": "^4.21.20", "@uiw/react-md-editor": "^3.25.6", "@wangeditor/editor": "^5.1.23", "ahooks": "^3.7.8", "antd": "^5.11.2", "axios": "^1.6.2", "classnames": "^2.3.2", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dom-to-image": "^2.6.0", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "echarts-wordcloud": "^2.1.0", "file-saver": "^2.0.5", "immer": "^10.0.3", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "react": "^18.2.0", "react-countup": "^6.5.0", "react-cropper": "^2.3.3", "react-dom": "^18.2.0", "react-org-tree": "^1.0.1", "react-redux": "^8.1.3", "react-rnd": "^10.4.1", "react-router-dom": "^6.21.1", "react-sortablejs": "^6.1.4", "redux": "^4.2.1", "redux-persist": "^6.0.0", "sortablejs": "^1.15.0", "use-immer": "^0.9.0", "video.js": "^8.6.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.23.3", "@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@eslint/js": "^9.25.1", "@types/crypto-js": "^4.2.1", "@types/dom-to-image": "^2.6.7", "@types/file-saver": "^2.0.7", "@types/loadable__component": "^5.13.8", "@types/lodash-es": "^4.17.12", "@types/node": "^20.9.2", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/sortablejs": "^1.15.5", "@types/video-react": "^0.15.4", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "commitizen": "^4.3.0", "cssnano": "^6.0.1", "cz-git": "^1.7.1", "esbuild": "^0.19.6", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "fast-glob": "^3.3.2", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "^15.1.0", "postcss": "^8.4.31", "postcss-load-config": "^5.0.2", "prettier": "^3.1.0", "rimraf": "^6.0.1", "rollup": "^4.5.1", "terser": "^5.24.0", "typescript": "^5.2.2", "vite": "6.2.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-mock": "2.9.8", "vite-plugin-svg-icons": "^2.0.1"}, "keywords": ["react", "react-router", "redux", "redux-toolkit", "vite", "typescript", "react-admin", "react-admin-design"], "license": "MIT", "homepage": "https://github.com/baimingxuan/react-admin-design", "repository": {"type": "git", "url": "git+https://github.com/baimingxuan/react-admin-design.git"}, "bugs": {"url": "https://github.com/baimingxuan/react-admin-design/issues"}, "engines": {"node": ">=16", "pnpm": ">=8"}, "lint-staged": {"{src,mock,build}/**/*.{js,ts,tsx}": ["eslint --fix"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "pnpm": {"allowedDeprecatedVersions": {"stable": "*", "abab": "*", "urix": "*", "domexception": "*", "w3c-hr-time": "*", "resolve-url": "*", "source-map-url": "*", "source-map-resolve": "*"}}}