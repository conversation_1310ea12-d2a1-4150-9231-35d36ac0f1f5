import { type FC, useEffect, useRef, useMemo, useCallback } from 'react'
import * as echarts from 'echarts/core'
import { GridComponent, TooltipComponent } from 'echarts/components'
import { LineChart as EchartsLineChart } from 'echarts/charts'
import { UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'

// Register necessary components
echarts.use([GridComponent, TooltipComponent, EchartsLineChart, UniversalTransition, CanvasRenderer])

interface LineChartProps {
  loading: boolean
  height?: number
  width?: number
  color?: string
  xData?: (string | number)[]
  yData?: number[]
}

const LineChart: FC<LineChartProps> = ({
  loading,
  height = 50,
  width = 100,
  color = '#18FEFE',
  xData = ['2025-07-08', '2025-07-08', '2025-07-08', '2025-07-08', '2025-07-08', '2025-07-08'],
  yData = [10, 50, 30, 40, 50, 60]
}) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstanceRef = useRef<echarts.ECharts | null>(null)

  // 使用useMemo优化图表配置，避免每次渲染都重新计算
  const chartOptions = useMemo(() => {
    // 使用传入的数据或默认数据
    const actualXData =
      xData && xData.length > 0
        ? xData
        : ['2025-07-08', '2025-07-08', '2025-07-08', '2025-07-08', '2025-07-08', '2025-07-08']
    const actualYData = yData && yData.length > 0 ? yData : [10, 50, 30, 20, 50, 40]

    console.log('LineChart - 使用的 xData:', actualXData)
    console.log('LineChart - 使用的 yData:', actualYData)

    return {
      backgroundColor: 'rgba(0, 10, 41, 0.3)',
      tooltip: {
        trigger: 'axis',
        formatter: function (params: any) {
          const dataIndex = params[0].dataIndex
          const value = params[0].value
          const date = actualXData[dataIndex]
          return `${date}<br/>总量: ${value}`
        },
        axisPointer: {
          type: 'none'
        }
      },
      grid: {
        left: 5,
        right: 5,
        top: 5,
        bottom: 5,
        containLabel: false,
        show: true,
        backgroundColor: 'rgba(18, 155, 255, 0.05)',
        borderColor: 'rgba(18, 155, 255, 0.1)'
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        show: true,
        axisLabel: {
          show: true
        },
        data: actualXData,
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(18, 155, 255, 0.2)',
            width: 1,
            type: 'solid'
          }
        },
        splitNumber: 6,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        show: true,
        axisLabel: {
          show: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(18, 155, 255, 0.2)',
            width: 1,
            type: 'solid'
          }
        },
        splitNumber: 6,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      series: [
        {
          data: actualYData,
          type: 'line',
          smooth: true,
          symbol: 'none',
          lineStyle: {
            color: color,
            width: 2
          }
        }
      ]
    }
  }, [xData, yData, color]) // 依赖项优化

  // 使用useCallback优化resize处理函数
  const handleResize = useCallback(() => {
    chartInstanceRef.current?.resize()
  }, [])

  // 初始化图表
  const initChart = useCallback(() => {
    const el = chartRef.current
    if (!el) return

    // 如果已存在实例，先销毁
    if (chartInstanceRef.current) {
      chartInstanceRef.current.dispose()
    }

    chartInstanceRef.current = echarts.init(el)
    chartInstanceRef.current.setOption(chartOptions)
  }, [chartOptions])

  useEffect(() => {
    initChart()
    window.addEventListener('resize', handleResize)

    return () => {
      chartInstanceRef.current?.dispose()
      window.removeEventListener('resize', handleResize)
    }
  }, [initChart, handleResize])

  // 数据变化时更新图表
  useEffect(() => {
    if (!loading && chartInstanceRef.current) {
      chartInstanceRef.current.setOption(chartOptions, true) // 使用notMerge优化性能
    }
  }, [loading, chartOptions])

  return <div ref={chartRef} style={{ width: '240px', height: `${height}px` }} />
}

export default LineChart
