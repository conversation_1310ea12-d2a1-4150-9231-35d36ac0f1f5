/* 数据登记总量弹窗样式 */

.ant-table-container table {
  background-color: transparent !important;
}

.ant-table-thead > tr > th {
  background-color: rgba(24, 144, 255, 0.2) !important;
  color: #858c9f !important;
  border-bottom: 1px solid #041138 !important;
}

.ant-table-tbody > tr {
  background-color: #061a49 !important;
}

.ant-table-tbody > tr > td {
  background-color: #061a49 !important;
  border-bottom: 1px solid #1a60a5 !important;
  color: #858c9f !important;
}

.ant-table-tbody > tr:hover > td {
  background-color: rgba(24, 144, 255, 0.1) !important;
}

.ant-pagination-item-active {
  border-color: #167bdb !important;
  background-color: transparent !important;
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  color: #858c9f !important;
}

.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis,
.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis {
  color: #858c9f !important;
}

.ant-pagination-options-quick-jumper {
  color: #858c9f !important;
}

.ant-pagination-options-quick-jumper input {
  background-color: #00103f !important;
  border: 1px solid #041138 !important;
  color: #858c9f;
}

.ant-select-selector {
  background-color: #00103f !important;
  border: 1px solid #041138 !important;
  color: #858c9f !important;
}

.ant-select-selection-item {
  color: #858c9f !important;
}

.dataModalHeader {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 20px;
  gap: 15px;
}

.filterItem {
  display: flex;
  align-items: center;
  margin-right: 15px;

  span {
    margin-right: 8px;
    color: white;
  }

  input {
    background-color: rgba(4, 17, 56, 0.7);
    border: 1px solid #041138;
    color: white;
    padding: 4px 8px;
    width: 160px;
  }
}

/* 强制覆盖表格背景色 */
.dataModal {
  color: #1890ff;
  .ant-table {
    background: transparent !important;
  }

  .ant-table-wrapper {
    background: transparent !important;
  }

  .ant-table-container {
    background: transparent !important;
  }

  .ant-table-content {
    background: transparent !important;
  }

  table {
    background: transparent !important;
    font-family: 'MySourceHanSans', sans-serif;
  }

  .ant-table-thead > tr > th {
    background: rgba(24, 144, 255, 0.2) !important;
    color: #dbe0e7 !important;
    padding: 10px 16px !important;
    font-weight: 500 !important;
  }
  .ant-table-tbody > tr {
    background-color: #061a49 !important;
    padding: 8px 16px !important;
  }

  .ant-table-tbody > tr > td {
    background-color: #061a49 !important;
    border-bottom: 1px solid #1a60a5 !important;
    color: #dbe0e7 !important;
    padding: 8px 16px !important;
  }

  .ant-table-tbody > tr.ant-table-row:hover > td {
    background: rgba(24, 144, 255, 0.1) !important;
  }

  .ant-table-cell {
    background: transparent !important;
  }
  // 时间选择
  .ant-picker {
    background: rgba(24, 144, 255, 0.1) !important;
    color: aliceblue;
  }
  input {
    background-color: rgba(4, 17, 56, 0.7);
    border: 1px solid #041138;
    color: white;
    padding: 4px 8px;
    width: 160px;
  }

  :where(.css-dev-only-do-not-override-36gkoj).ant-input-outlined:focus,
  :where(.css-dev-only-do-not-override-36gkoj).ant-input-outlined:focus-within {
    background: rgba(24, 144, 255, 0.1) !important;
    color: aliceblue;
  }
}

:where(.css-dev-only-do-not-override-1m63z2v).ant-table-wrapper
  .ant-table-container
  table
  > thead
  > tr:first-child
  > *:first-child {
  border-start-start-radius: 0;
}

:where(.css-dev-only-do-not-override-1m63z2v).ant-table-wrapper
  .ant-table-container
  table
  > thead
  > tr:first-child
  > *:last-child {
  border-start-start-radius: 0;
}
:where(.css-dev-only-do-not-override-1m63z2v).ant-modal .ant-modal-content {
  background-color: transparent !important;
}
:where(.css-dev-only-do-not-override-36gkoj).ant-modal .ant-modal-content {
  background-color: transparent !important;
}
.ant-pagination {
  margin-top: 24px;
}

.ant-table {
  margin-bottom: 12px;
}
:where(.css-dev-only-do-not-override-36gkoj).ant-btn-variant-solid:disabled,
:where(.css-dev-only-do-not-override-36gkoj).ant-btn-variant-solid.ant-btn-disabled {
  border-color: #167bdb;
}

/* 隐藏表格滚动条占位符 */
.ant-table-thead > tr > th.ant-table-cell-scrollbar {
  display: none !important;
}

/* 隐藏表格滚动条列 */
.ant-table-tbody > tr > td.ant-table-cell-scrollbar {
  display: none !important;
}

/* 隐藏多余的表头行 - 兼容性更好的方案 */
.ant-table-thead > tr:first-child th:only-child:empty {
  display: none !important;
}

.ant-table-thead > tr:first-child:empty {
  display: none !important;
}

/* 隐藏高度很小的表头行 */
.ant-table-thead > tr[style*='height: 0'] {
  display: none !important;
}

.ant-table-thead > tr[style*='height:0'] {
  display: none !important;
}

/* 修复表格宽度 */
.ant-table-container {
  overflow-x: hidden !important;
}

/* 确保表格内容区域正确显示 */
.ant-table-body {
  overflow-x: hidden !important;
}
.dataModal :where(.css-dev-only-do-not-override-36gkoj).ant-input-outlined:focus,
.dataModal :where(.css-dev-only-do-not-override-36gkoj).ant-input-outlined:focus-within {
  background: #00103f !important;
}

/* 重置按钮和搜索按钮样式 */
.reset-btn {
  min-width: 90px !important;
  width: 90px !important;
}

.dataModal .ant-btn[type='submit'],
.dataModal .ant-btn-primary {
  min-width: 90px !important;
  width: 90px !important;
}

/* 通用按钮宽度设置 */
.dataModal .ant-btn {
  min-width: 90px !important;
}
:where(.css-dev-only-do-not-override-1m63z2v).ant-btn-variant-solid:not(:disabled):not(.ant-btn-disabled):hover {
  background: rgba(24, 144, 255, 0.1) !important;
}
.ant-btn-primary {
  background: rgba(24, 144, 255, 0.1) !important;
}
:where(.css-dev-only-do-not-override-1m63z2v).ant-pagination
  .ant-pagination-item:not(.ant-pagination-item-active):hover {
  border-color: #167bdb !important;
  background-color: transparent !important;
  color: #1677ff !important;
}
