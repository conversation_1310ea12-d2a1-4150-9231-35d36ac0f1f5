@color: #1890ff;
@size: 8px;
@offset: -(@size / 2);

.rnd-node-wrapper {
  border: 1px dashed transparent;
  box-sizing: content-box !important;

  &.active {
    border-color: #1890ff;

    .handler {
      display: flex !important;
    }
  }

  .handler {
    display: none;
    width: @size !important;
    height: @size !important;
    border: 1px solid @color;
    border-radius: 50%;
    background: #fff;
    box-sizing: border-box;

    &.handler-n {
      top: @offset !important;
      left: 50% !important;
      margin-left: @offset;
      cursor: n-resize !important;
    }

    &.handler-e {
      top: 50% !important;
      right: 0 !important;
      margin-right: @offset;
      margin-top: @offset;
      cursor: e-resize !important;
    }

    &.handler-s {
      bottom: 0 !important;
      left: 50% !important;
      margin-left: @offset;
      margin-bottom: @offset;
      cursor: s-resize !important;
    }

    &.handler-w {
      top: 50% !important;
      left: 0 !important;
      margin-top: @offset;
      margin-left: @offset;
      cursor: w-resize !important;
    }

    &.handler-ne {
      top: 0 !important;
      right: 0 !important;
      margin-top: @offset;
      margin-right: @offset;
      cursor: ne-resize !important;
    }

    &.handler-nw {
      top: 0 !important;
      left: 0 !important;
      margin-top: @offset;
      margin-left: @offset;
      cursor: nw-resize !important;
    }

    &.handler-se {
      right: 0 !important;
      bottom: 0 !important;
      margin-right: @offset;
      margin-bottom: @offset;
      cursor: se-resize !important;
    }

    &.handler-sw {
      bottom: 0 !important;
      left: 0 !important;
      margin-left: @offset;
      margin-bottom: @offset;
      cursor: sw-resize !important;
    }
  }

}
