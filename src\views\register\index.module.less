.registerWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  background: #f5f7fa;
}

.registerContainer {
  display: flex;
  width: 940px;
  height: 520px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background: #fff;
}

.registerLeft {
  flex: 1;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  position: relative;
  overflow: hidden;
  
  // Design elements for illustration effect
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    transform: translate(-50%, -50%) rotate(45deg);
    z-index: 2;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 240px;
    height: 240px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    transform: translate(-50%, -50%) rotate(45deg);
    z-index: 3;
  }
}

// Add laptop illustration effect with pure CSS
.laptopIllustration {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 280px;
  height: 180px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px 10px 0 0;
  z-index: 4;
  
  &::before {
    content: '';
    position: absolute;
    bottom: -20px;
    left: -20px;
    width: 320px;
    height: 20px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 0 0 5px 5px;
    transform: perspective(500px) rotateX(20deg);
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    background: rgba(29, 144, 244, 0.8);
    border-radius: 5px;
  }
}

.registerRight {
  width: 460px;
  padding: 30px 40px;
  background: #fff;
}

.registerHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 2px solid #1890ff;
}

.registerTab {
  font-size: 20px;
  font-weight: 600;
  color: #1890ff;
  position: relative;
  padding-bottom: 10px;
  
  &::after {
    content: '';
    position: absolute;
    bottom: -17px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #1890ff;
  }
}

.loginLink {
  font-size: 14px;
  color: #666;
  text-decoration: none;
}

.registerForm {
  padding-top: 10px;
  
  .ant-form-item {
    margin-bottom: 24px;
  }
  
  .ant-input {
    height: 46px;
    border-radius: 4px;
  }
  
  .ant-checkbox + span {
    font-size: 14px;
    
    a {
      color: #1890ff;
    }
  }
}

.inputIcon {
  color: #bfbfbf;
}

.verificationCodeBtn {
  padding: 0 15px;
  height: 100%;
  color: #1890ff;
  font-size: 14px;
  border: none;
  box-shadow: none;
  
  &:hover, &:focus {
    color: #40a9ff;
  }
}

.registerBtn {
  width: 100%;
  height: 46px;
  border-radius: 4px;
  font-size: 16px;
  background-color: #1890ff;
  border-color: #1890ff;
  
  &:hover, &:focus {
    background-color: #40a9ff;
    border-color: #40a9ff;
  }
}
