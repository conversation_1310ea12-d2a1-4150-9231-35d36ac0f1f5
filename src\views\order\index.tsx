import type { ColumnsType } from 'antd/es/table'
import { type FC, useState, useEffect } from 'react'
import { type TableProps, Card, Button, Table, Radio, Space, Form, Input, Pagination, Modal, Row, Col } from 'antd'
import { SearchOutlined, CloseOutlined } from '@ant-design/icons'
import { getTableList } from '@/api'
import { PageWrapper } from '@/components/Page'
import type { APIResult, PageState, TableDataType } from './types'

const OrderPage: FC = () => {
  const [tableLoading, setTableLoading] = useState(false)
  const [tableData, setTableData] = useState<TableDataType[]>([])
  const [tableTotal, setTableTotal] = useState<number>(0)
  const [tableQuery, setTableQuery] = useState<PageState>({ current: 1, pageSize: 10 })
  const [searchType, setSearchType] = useState<'keyword' | 'exact'>('keyword')
  const [certificateVisible, setCertificateVisible] = useState(false)
  const [currentOrder, setCurrentOrder] = useState<TableDataType | null>(null)

  const [form] = Form.useForm()

  const columns: ColumnsType<TableDataType> = [
    {
      title: '序号',
      dataIndex: 'serialNumber',
      align: 'center',
      width: 80
    },
    {
      title: '订单名称',
      dataIndex: 'orderName',
      align: 'center'
    },
    {
      title: '订单编号',
      dataIndex: 'orderNumber',
      align: 'center'
    },
    {
      title: '签约有效期',
      dataIndex: 'validityPeriod',
      align: 'center'
    },
    {
      title: '交易凭证',
      dataIndex: 'transactionCertificate',
      align: 'center',
      render: (_, record) => (
        <a
          href='#'
          style={{ color: '#1890ff' }}
          onClick={e => {
            e.preventDefault()
            showCertificate(record)
          }}
        >
          查看详情
        </a>
      )
    },
    {
      title: '订单任务',
      dataIndex: 'orderTask',
      align: 'center',
      render: () => (
        <Space>
          <a href='#' style={{ color: '#1890ff' }}>
            查看详情
          </a>
          <a href='#' style={{ color: '#1890ff' }}>
            创建任务
          </a>
        </Space>
      )
    }
  ]

  useEffect(() => {
    fetchData()
  }, [tableQuery])

  async function fetchData() {
    setTableLoading(true)
    try {
      const data = await getTableList(tableQuery)
      const { list, total } = data as unknown as APIResult
      setTableData(list)
      setTableTotal(total)
    } catch (error) {
      console.error('获取数据失败:', error)
      // 模拟数据用于展示
      const mockData = Array(10)
        .fill(0)
        .map((_, index) => ({
          id: index + 1,
          serialNumber: '232',
          orderName: '用户行为统计分析',
          orderNumber: 'SZDEXRE0217403264001',
          validityPeriod: '2025-01-01/2026-01-01',
          transactionCertificate: '',
          orderTask: ''
        }))
      setTableData(mockData)
      setTableTotal(200)
    } finally {
      setTableLoading(false)
    }
  }

  function handlePageChange(page: number, pageSize?: number) {
    setTableQuery({
      ...tableQuery,
      current: page,
      ...(pageSize ? { pageSize } : {})
    })
  }

  function handleSearch(values: any) {
    setTableQuery({ ...tableQuery, search: values.search, current: 1 })
  }

  function handleSearchTypeChange(e: any) {
    setSearchType(e.target.value)
  }

  function showCertificate(record: TableDataType) {
    setCurrentOrder(record)
    setCertificateVisible(true)
  }

  function handleCloseCertificate() {
    setCertificateVisible(false)
    setCurrentOrder(null)
  }

  return (
    <Card bordered={false}>
      <h2 style={{ marginBottom: '20px', fontWeight: 'bold' }}>交付订单目录</h2>

      <div
        style={{
          backgroundColor: '#f5f5f5',
          padding: '10px 16px',
          marginBottom: '16px',
          display: 'flex',
          alignItems: 'center'
        }}
      >
        <Form layout='inline' onFinish={handleSearch} initialValues={{ search: '' }} form={form} style={{ flex: 1 }}>
          <Form.Item name='search' style={{ width: 250, marginRight: 0 }}>
            <Input
              placeholder='数据名称/注册编号'
              prefix={<SearchOutlined style={{ color: '#bfbfbf' }} rev={undefined} />}
            />
          </Form.Item>
          <Form.Item>
            <Button type='primary' htmlType='submit' style={{ marginLeft: 8 }}>
              查询
            </Button>
          </Form.Item>
          <Form.Item>
            <Button
              htmlType='button'
              onClick={() => {
                form.setFieldsValue({ search: '' })
                setTableQuery({ ...tableQuery, search: '', current: 1 })
              }}
              style={{ marginLeft: 8 }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
      </div>

      <Table
        rowKey='id'
        columns={columns}
        dataSource={tableData}
        loading={tableLoading}
        pagination={false}
        bordered
        size='middle'
      />

      <div
        style={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: '16px',
          alignItems: 'center'
        }}
      >
        <span style={{ marginRight: '16px' }}>共 {tableTotal} 条</span>
        <Pagination
          current={tableQuery.current}
          pageSize={tableQuery.pageSize}
          total={tableTotal}
          showTotal={() => `共${tableTotal}条`}
          showSizeChanger={true}
          showQuickJumper={true}
          pageSizeOptions={['10', '20', '50', '100']}
          onChange={(page, pageSize) => handlePageChange(page, pageSize)}
          locale={{
            items_per_page: '条/页',
            jump_to: '跳至',
            jump_to_confirm: '确定',
            page: '页',
            prev_page: '上一页',
            next_page: '下一页',
            prev_5: '向前 5 页',
            next_5: '向后 5 页',
            prev_3: '向前 3 页',
            next_3: '向后 3 页'
          }}
        />
      </div>

      {/* 交易凭证弹框 */}
      <Modal
        title='订单交易凭证'
        open={certificateVisible}
        footer={null}
        onCancel={handleCloseCertificate}
        closeIcon={<CloseOutlined />}
        width={600}
      >
        <div style={{ backgroundColor: '#f5f5f5', padding: '20px' }}>
          <Row>
            <Col span={8} style={{ textAlign: 'right', paddingRight: '20px', color: '#888' }}>
              凭证名称
            </Col>
            <Col span={16}>凭证内容</Col>
          </Row>
          <Row style={{ marginTop: '15px' }}>
            <Col span={8} style={{ textAlign: 'right', paddingRight: '20px', color: '#888' }}>
              订单名称
            </Col>
            <Col span={16}>{currentOrder?.orderName || 'XXXXXXXXXX'}</Col>
          </Row>
          <Row style={{ marginTop: '15px' }}>
            <Col span={8} style={{ textAlign: 'right', paddingRight: '20px', color: '#888' }}>
              订单编号
            </Col>
            <Col span={16}>{currentOrder?.orderNumber || 'SZDEXRE0217403264001'}</Col>
          </Row>
          <Row style={{ marginTop: '15px' }}>
            <Col span={8} style={{ textAlign: 'right', paddingRight: '20px', color: '#888' }}>
              任务类型
            </Col>
            <Col span={16}>医疗查询</Col>
          </Row>
          <Row style={{ marginTop: '15px' }}>
            <Col span={8} style={{ textAlign: 'right', paddingRight: '20px', color: '#888' }}>
              任务发起方
            </Col>
            <Col span={16}>中移动信息技术有限公司</Col>
          </Row>
          <Row style={{ marginTop: '15px' }}>
            <Col span={8} style={{ textAlign: 'right', paddingRight: '20px', color: '#888' }}>
              任务协作方
            </Col>
            <Col span={16}>腾讯科技有限公司</Col>
          </Row>
          <Row style={{ marginTop: '15px' }}>
            <Col span={8} style={{ textAlign: 'right', paddingRight: '20px', color: '#888' }}>
              算力配置计量
            </Col>
            <Col span={16}>CPU: 16核 内存: 32G 时长: 5/小时 (10元/小时)</Col>
          </Row>
          <Row style={{ marginTop: '15px' }}>
            <Col span={8} style={{ textAlign: 'right', paddingRight: '20px', color: '#888' }}>
              数据产品计量
            </Col>
            <Col span={16}>使用次数: 10000 (0.1元/次)</Col>
          </Row>
          <Row style={{ marginTop: '15px' }}>
            <Col span={8} style={{ textAlign: 'right', paddingRight: '20px', color: '#888' }}>
              订单计费合计
            </Col>
            <Col span={16} style={{ backgroundColor: '#d6e4ff', padding: '8px' }}>
              XXXXX元
            </Col>
          </Row>
        </div>
      </Modal>
    </Card>
  )
}

export default OrderPage
