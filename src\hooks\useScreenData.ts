import { useState, useCallback, useEffect } from 'react'
import { message } from 'antd'
import { useDataCache } from './useDataCache'
// 注释掉所有接口请求，使用本地JSON数据
// import {
//   getCount,
//   dataTrendChart,
//   dataListedCount,
//   dataContractListedSignCount,
//   dataExList,
//   dataCalculatedCount
// } from '@/api/screen'

// 导入本地JSON数据
import screenDataJson from '@/data/screenData.json'

interface ScreenDataState {
  counts: string
  xData: any[]
  yData: any[]
  listedCount: number
  listedCompareRate: number
  signCount: number
  signCompareRate: number
  tradedCount: number
  pendingCount: number
  exchangeData: any[]
  isDataLoaded: boolean
}

/**
 * 大屏数据管理Hook，优化数据加载性能
 */
export function useScreenData() {
  // 注释掉useDataCache，直接使用本地数据，不需要缓存
  // const { getData, clearCache, loading } = useDataCache<any>({
  //   cacheTime: 5 * 60 * 1000, // 5分钟缓存
  //   staleTime: 30 * 1000 // 30秒内数据视为新鲜
  // })

  // 使用本地状态管理loading
  const [loading, setLoading] = useState(false)

  // 直接使用本地JSON数据初始化状态
  const [screenData, setScreenData] = useState<ScreenDataState>({
    counts: screenDataJson.data.counts,
    xData: screenDataJson.data.xData,
    yData: screenDataJson.data.yData,
    listedCount: screenDataJson.data.listedCount,
    listedCompareRate: screenDataJson.data.listedCompareRate,
    signCount: screenDataJson.data.signCount,
    signCompareRate: screenDataJson.data.signCompareRate,
    tradedCount: screenDataJson.data.tradedCount,
    pendingCount: screenDataJson.data.pendingCount,
    exchangeData: screenDataJson.data.exchanges,
    isDataLoaded: true // 本地数据已加载
  })

  // 获取数据统计 - 使用本地JSON数据
  const fetchDataCount = useCallback(async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.counts
      setScreenData(prev => ({ ...prev, counts: result || '0' }))
      return result
    } catch (error) {
      console.error('获取数据统计失败:', error)
      message.error('获取数据统计失败，请稍后重试')
      return '0'
    }
  }, [])

  // 获取趋势图数据 - 使用本地JSON数据
  const fetchTrendChart = useCallback(async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const xDataResult = screenDataJson.data.xData
      const yDataResult = screenDataJson.data.yData

      setScreenData(prev => ({
        ...prev,
        xData: xDataResult,
        yData: yDataResult
      }))

      return { xData: xDataResult, yData: yDataResult }
    } catch (error) {
      console.error('趋势图数据获取失败:', error)
      message.error('获取趋势图数据失败，请稍后重试')
    }
  }, [])

  // 获取挂牌数量 - 使用本地JSON数据
  const fetchListedCount = useCallback(async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.listedCount
      if (result) {
        setScreenData(prev => {
          const totalCount = parseInt(screenDataJson.data.counts) || 1
          const percentage = totalCount > 0 ? Math.round((result / totalCount) * 100) : 0
          return {
            ...prev,
            listedCount: result || 0,
            listedCompareRate: percentage
          }
        })
      }
      return result
    } catch (error) {
      console.error('数据挂牌数量获取失败:', error)
    }
  }, [])

  // 获取签约数量 - 使用本地JSON数据
  const fetchSignCount = useCallback(async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.signCount
      if (result) {
        setScreenData(prev => {
          const totalCount = parseInt(screenDataJson.data.counts) || 1
          const percentage = totalCount > 0 ? Math.round((result / totalCount) * 100) : 0
          return {
            ...prev,
            signCount: result || 0,
            signCompareRate: percentage
          }
        })
      }
      return result
    } catch (error) {
      console.error('获取数据签约数量失败:', error)
    }
  }, [])

  // 获取交易所列表 - 使用本地JSON数据
  const fetchExchangeList = useCallback(async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const result = screenDataJson.data.exchanges
      if (result) {
        setScreenData(prev => ({ ...prev, exchangeData: result }))
      }
      return result
    } catch (error) {
      console.error('获取交易所列表失败:', error)
    }
  }, [])

  // 获取交易统计 - 使用本地JSON数据
  const fetchCalculatedCount = useCallback(async () => {
    try {
      // 使用本地JSON数据替代接口请求
      const tradedCount = screenDataJson.data.tradedCount
      const pendingCount = screenDataJson.data.pendingCount

      setScreenData(prev => ({
        ...prev,
        tradedCount: tradedCount || 0,
        pendingCount: pendingCount || 0
      }))

      return { tradedCount, pendingCount }
    } catch (error) {
      console.error('获取数据交易统计失败:', error)
      message.error('获取数据交易统计失败，请稍后重试')
    }
  }, [])

  // 加载所有初始数据 - 使用本地JSON数据，不需要请求
  const loadInitialData = useCallback(async () => {
    try {
      // 数据已经在初始化时加载，这里不需要做任何事情
      console.log('使用本地JSON数据，无需加载')
      // 确保isDataLoaded为true
      setScreenData(prev => ({ ...prev, isDataLoaded: true }))
    } catch (error) {
      console.error('初始数据加载失败:', error)
      message.error('数据加载失败，请刷新页面重试')
    }
  }, [])

  // 刷新所有数据 - 使用本地数据，不需要刷新
  const refreshAllData = useCallback(() => {
    console.log('使用本地JSON数据，无需刷新')
    // 本地数据不需要刷新
  }, [])

  // 刷新特定数据 - 使用本地数据，不需要刷新
  const refreshData = useCallback(
    (dataType: string) => {
      console.log('使用本地JSON数据，无需刷新特定数据:', dataType)
      // 本地数据不需要刷新
    },
    []
  )

  return {
    screenData,
    loading,
    loadInitialData,
    refreshAllData,
    refreshData,
    // 单独的获取函数，供组件按需调用
    fetchDataCount,
    fetchTrendChart,
    fetchListedCount,
    fetchSignCount,
    fetchExchangeList,
    fetchCalculatedCount
  }
}
