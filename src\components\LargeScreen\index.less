* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
.layout_wrapper {
  position: relative;
  width: 100vw;
  height: 100vh;
  // background: url('../assets/home/<USER>') no-repeat;
  // background-size: 100% 100%;
  background-color: #000a29;
  overflow: hidden;
  color: #fff;
}

.wrapper {
  overflow: hidden;
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: center center;
  
  background: url('@/assets/images/bg.png') no-repeat center;
  background-size: 95% 90%;
}
