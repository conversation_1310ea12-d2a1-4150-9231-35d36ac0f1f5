import { useState, useCallback, useRef } from 'react'

interface CacheItem<T> {
  data: T
  timestamp: number
  expiry: number
}

interface UseDataCacheOptions {
  cacheTime?: number // 缓存时间，默认5分钟
  staleTime?: number // 数据过期时间，默认1分钟
}

/**
 * 数据缓存Hook，用于优化API请求性能
 * @param options 缓存配置选项
 */
export function useDataCache<T>(options: UseDataCacheOptions = {}) {
  const { cacheTime = 5 * 60 * 1000, staleTime = 1 * 60 * 1000 } = options

  const cacheRef = useRef<Map<string, CacheItem<T>>>(new Map())
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  /**
   * 获取缓存数据
   * @param key 缓存键
   * @param fetcher 数据获取函数
   * @param forceRefresh 是否强制刷新
   */
  const getData = useCallback(
    async (key: string, fetcher: () => Promise<T>, forceRefresh = false): Promise<T> => {
      const now = Date.now()
      const cached = cacheRef.current.get(key)

      // 检查缓存是否有效且未过期
      if (!forceRefresh && cached && now < cached.expiry) {
        return cached.data
      }

      // 检查数据是否仍然新鲜（在staleTime内）
      if (!forceRefresh && cached && now - cached.timestamp < staleTime) {
        // 后台更新数据
        fetcher()
          .then(newData => {
            cacheRef.current.set(key, {
              data: newData,
              timestamp: now,
              expiry: now + cacheTime
            })
          })
          .catch(console.error)

        return cached.data
      }

      setLoading(true)
      setError(null)

      try {
        const data = await fetcher()

        // 缓存新数据
        cacheRef.current.set(key, {
          data,
          timestamp: now,
          expiry: now + cacheTime
        })

        return data
      } catch (err) {
        const error = err instanceof Error ? err : new Error('数据获取失败')
        setError(error)

        // 如果有缓存数据，返回缓存数据
        if (cached) {
          console.warn('使用缓存数据，因为请求失败:', error.message)
          return cached.data
        }

        throw error
      } finally {
        setLoading(false)
      }
    },
    [cacheTime, staleTime]
  )

  /**
   * 清除指定缓存
   * @param key 缓存键，不传则清除所有缓存
   */
  const clearCache = useCallback((key?: string) => {
    if (key) {
      cacheRef.current.delete(key)
    } else {
      cacheRef.current.clear()
    }
  }, [])

  /**
   * 预加载数据
   * @param key 缓存键
   * @param fetcher 数据获取函数
   */
  const prefetch = useCallback(
    async (key: string, fetcher: () => Promise<T>) => {
      const cached = cacheRef.current.get(key)
      const now = Date.now()

      // 如果缓存有效，不需要预加载
      if (cached && now < cached.expiry) {
        return
      }

      try {
        const data = await fetcher()
        cacheRef.current.set(key, {
          data,
          timestamp: now,
          expiry: now + cacheTime
        })
      } catch (error) {
        console.error('预加载失败:', error)
      }
    },
    [cacheTime]
  )

  /**
   * 获取缓存状态
   * @param key 缓存键
   */
  const getCacheStatus = useCallback(
    (key: string) => {
      const cached = cacheRef.current.get(key)
      if (!cached) return { exists: false, isStale: false, isExpired: true }

      const now = Date.now()
      const isExpired = now >= cached.expiry
      const isStale = now - cached.timestamp >= staleTime

      return {
        exists: true,
        isStale,
        isExpired,
        age: now - cached.timestamp
      }
    },
    [staleTime]
  )

  return {
    getData,
    clearCache,
    prefetch,
    getCacheStatus,
    loading,
    error
  }
}
