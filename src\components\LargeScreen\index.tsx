import React, { useState, useEffect, type ReactNode } from 'react'
import './index.less'

interface IndexProps {
  width: number
  height: number
  children?: ReactNode
}

const LargeScreen: React.FC<IndexProps> = props => {
  const [scale, setScale] = useState(1)

  useEffect(() => {
    function handleResize() {
      // 获取窗口的宽度和高度
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      // 使用传入的宽高作为理想尺寸
      const idealWidth = props.width
      const idealHeight = props.height
      const newScale = Math.min(windowWidth / idealWidth, windowHeight / idealHeight)
      // 设置新的缩放比例
      setScale(newScale)
    }

    // 初始加载时调用一次
    handleResize()
    // 添加resize事件监听器
    window.addEventListener('resize', handleResize)

    // 清理函数，移除监听器
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [props.width, props.height])

  return (
    <div className='layout_wrapper'>
      <div
        className='wrapper'
        style={{
          transform: `translate(-50%, -50%) scale(${scale})`,
          width: props.width + 'px',
          height: props.height + 'px'
        }}
      >
        {props.children}
      </div>
    </div>
  )
}
export { LargeScreen }
