import React from 'react'
import { Table, Pagination, Button } from 'antd'
import type { TableProps, PaginationProps } from 'antd'
import './DataTable.less'
interface DataTableProps<T> extends Omit<TableProps<T>, 'pagination'> {
  paginationProps?: {
    current?: number
    total?: number
    onChange?: (page: number, pageSize: number) => void
    pageSize?: number
    showSizeChanger?: boolean
  }
  onRowClick?: (record: T) => void
}

const DataTable = <T extends object>({
  columns,
  dataSource,
  paginationProps,
  onRowClick,

  ...restProps
}: DataTableProps<T>) => {
  const handlePageChange = (page: number, pageSize: number) => {
    paginationProps?.onChange?.(page, pageSize)
  }

  // 行点击处理
  const onRow = (record: T) => {
    return {
      onClick: () => {
        if (onRowClick) {
          onRowClick(record)
        }
      },
      style: { cursor: onRowClick ? 'pointer' : 'default' }
    }
  }

  return (
    <>
      <Table
        columns={columns}
        // scroll={{ y: 40 * 5 }}
        dataSource={dataSource}
        pagination={false}
        onRow={onRow}
        // style={{ minHeight: '400px' }}
        {...restProps}
      />
      {paginationProps && (
        <Pagination
          align='end'
          current={paginationProps.current || 1}
          total={paginationProps.total || 0}
          pageSize={paginationProps.pageSize || 10}
          onChange={handlePageChange}
          showSizeChanger={paginationProps.showSizeChanger}
          itemRender={(page, type, originalElement) => {
            if (type === 'prev') {
              return (
                <Button type='primary' style={{ color: '#167bdb' }}>
                  上一页
                </Button>
              )
            }
            if (type === 'next') {
              return (
                <Button type='primary' style={{ color: '#167bdb' }}>
                  下一页
                </Button>
              )
            }
            return originalElement
          }}
        />
      )}
    </>
  )
}

export default DataTable
