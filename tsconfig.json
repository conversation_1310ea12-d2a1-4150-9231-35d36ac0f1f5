{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "baseUrl": ".", "allowJs": false, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "paths": {"@/*": ["src/*"], "#/*": ["types/*"]}, "types": ["vite/client", "vite-plugin-svg-icons/client"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "typings/**/*.d.ts", "typings/**/*.ts", "build/**/*.ts", "build/**/*.d.ts", "mock/*.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "**/*.js"]}