import type { ColumnsType } from 'antd/es/table'
import { type FC, useState, useEffect } from 'react'
import {
  type TableProps,
  Card,
  Button,
  Table,
  Tag,
  Switch,
  Popover,
  Space,
  Modal,
  Form,
  Input,
  Select,
  Checkbox
} from 'antd'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { TABLE_COMPO } from '@/settings/websiteSetting'
import { getTableList } from '@/api'
import { PageWrapper } from '@/components/Page'
import type { APIResult, PageState, TableDataType } from './types'

const marriedOptions = [
  { label: '单身', value: 0 },
  { label: '未婚', value: 1 },
  { label: '已婚', value: 2 },
  { label: '离异', value: 3 }
]

const TableBasic: FC = () => {
  const [tableLoading, setTableLoading] = useState(false)
  const [tableData, setTableData] = useState<TableDataType[]>([])
  const [tableTotal, setTableTotal] = useState<number>(0)
  const [tableQuery, setTableQuery] = useState<PageState>({ current: 1, pageSize: 10 })

  const [form] = Form.useForm()
  const [modalVisibel, setModalVisibel] = useState<boolean>(false)
  const [editHobbys, setEditHobbys] = useState<string[]>([])

  const columns: ColumnsType<any> = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
      render: (_: any, __: any, idx: number) => idx + 1 + (tableQuery.current - 1) * tableQuery.pageSize
    },
    {
      title: '机构主体名称',
      dataIndex: 'orgName',
      align: 'center'
    },
    {
      title: '用户手机号',
      dataIndex: 'phone',
      align: 'center'
    },
    {
      title: '统一信用代码',
      dataIndex: 'creditCode',
      align: 'center'
    },
    {
      title: '分布式身份DID标识',
      dataIndex: 'did',
      align: 'center'
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      render: (value: boolean) => (
        <span>
          <Switch checked={value} style={{ marginRight: 8 }} />
          <Tag color={value ? 'green' : 'default'}>{value ? '正常' : '启用'}</Tag>
        </span>
      )
    }
  ]

  const tableSelection: TableProps<any>['rowSelection'] = {
    onChange: (selectedRowKeys: any[]) => {
      console.log(selectedRowKeys)
    }
  }

  useEffect(() => {
    fetchData()
  }, [tableQuery])

  async function fetchData() {
    setTableLoading(true)
    const data = await getTableList(tableQuery)
    const { list, total } = data as unknown as APIResult
    setTableData(list)
    setTableTotal(total)
    setTableLoading(false)
  }

  function handlePageChange(page: number, pageSize: number) {
    setTableQuery({ ...tableQuery, current: page, pageSize })
  }

  function handleDelete() {
    Modal.confirm({
      title: '此操作将删除选中数据, 是否继续?',
      icon: <ExclamationCircleOutlined rev={undefined} />,
      // okType: 'danger',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        console.log('OK')
      },
      onCancel() {
        console.log('Cancel')
      }
    })
  }

  function handleEdit(record: TableDataType) {
    form.setFieldsValue({ ...record })
    setEditHobbys(record.hobby)
    setModalVisibel(true)
  }

  function handleConfirm() {
    // 调用接口
    setModalVisibel(false)
  }

  function handleCancle() {
    setEditHobbys([])
    setModalVisibel(false)
  }

  return (
    // <PageWrapper plugin={TABLE_COMPO}>
    <Card bordered={false}>
      <Form
        layout='inline'
        style={{ marginBottom: 16 }}
        onFinish={values => setTableQuery({ ...tableQuery, ...values, current: 1 })}
        initialValues={{ orgName: '', phone: '', creditCode: '' }}
        form={form}
      >
        <Form.Item name='search' style={{ width: 300 }}>
          <Input placeholder='机构主体名称/手机号/统一信用代码' />
        </Form.Item>
        <Form.Item>
          <Button type='primary' htmlType='submit'>
            查询
          </Button>
        </Form.Item>
        <Form.Item>
          <Button
            htmlType='button'
            onClick={() => {
              form.setFieldsValue({ search: '' })
              setTableQuery({ current: 1, pageSize: 10 })
            }}
          >
            重置
          </Button>
        </Form.Item>
      </Form>
      <Table
        rowKey='id'
        columns={columns}
        dataSource={tableData}
        loading={tableLoading}
        pagination={{
          current: tableQuery.current,
          pageSize: tableQuery.pageSize,
          total: tableTotal,
          showTotal: () => `共${tableTotal}条`,
          showSizeChanger: true,
          showQuickJumper: true,
          pageSizeOptions: ['10', '20', '50', '100'],
          onChange: handlePageChange,
          locale: {
            items_per_page: '条/页',
            jump_to: '跳至',
            jump_to_confirm: '确定',
            page: '页',
            prev_page: '上一页',
            next_page: '下一页',
            prev_5: '向前 5 页',
            next_5: '向后 5 页',
            prev_3: '向前 3 页',
            next_3: '向后 3 页'
          }
        }}
      />
      <Modal
        open={modalVisibel}
        title='编辑'
        width='600px'
        okText='确定'
        cancelText='取消'
        onCancel={handleCancle}
        onOk={handleConfirm}
      >
        <Form
          form={form}
          colon={false}
          labelCol={{ span: 4 }}
          labelAlign='left'
          style={{ width: '80%', margin: '0 auto' }}
        >
          <Form.Item label='姓名' name='name'>
            <Input disabled />
          </Form.Item>
          <Form.Item label='手机' name='phone'>
            <Input placeholder='请输入手机号码' />
          </Form.Item>
          <Form.Item label='学历' name='education'>
            <Select options={['初中', '高中', '大专', '本科'].map(item => ({ value: item }))} />
          </Form.Item>
          <Form.Item label='爱好' name='hobby'>
            <Checkbox.Group options={editHobbys.map(item => ({ label: item, value: item }))} />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
    // </PageWrapper>
  )
}

export default TableBasic
