{"description": "大屏展示数据 - 用于演示效果，替代真实接口请求", "data": {"counts": "30000", "xData": ["01-01", "01-02", "01-03", "01-04", "01-05", "01-06", "01-07", "01-08", "01-09", "01-10"], "yData": [120, 132, 101, 134, 90, 230, 210, 182, 191, 234], "listedCount": 15000, "listedCompareRate": 50, "signCount": 6000, "signCompareRate": 20, "tradedCount": 40, "pendingCount": 20, "exchanges": [{"dataExName": "杭州数据交易所", "signedContractsCount": 30, "distinctBuyerUsersCount": 10, "listedDataCount": 1000}, {"dataExName": "郑州数据交易所", "signedContractsCount": 25, "distinctBuyerUsersCount": 8, "listedDataCount": 800}, {"dataExName": "深圳数据交易所", "signedContractsCount": 35, "distinctBuyerUsersCount": 12, "listedDataCount": 1200}, {"dataExName": "北京数据交易所", "signedContractsCount": 40, "distinctBuyerUsersCount": 15, "listedDataCount": 1500}, {"dataExName": "上海数据交易所", "signedContractsCount": 45, "distinctBuyerUsersCount": 18, "listedDataCount": 1800}, {"dataExName": "广州数据交易所", "signedContractsCount": 28, "distinctBuyerUsersCount": 9, "listedDataCount": 900}], "dataList": {"records": [{"dataId": "1", "dataName": "智慧城市交通数据", "dataRegisterNumber": "A0001111", "listedNumber": "L0001111", "contractNumber": "C0001111", "dataCategory": "公共数据", "dataSecondCategory": "城市管理", "dataAccessLevel": "公开"}, {"dataId": "2", "dataName": "金融风控数据集", "dataRegisterNumber": "A0001112", "listedNumber": "L0001112", "contractNumber": "C0001112", "dataCategory": "企业数据", "dataSecondCategory": "金融", "dataAccessLevel": "指定机构"}, {"dataId": "3", "dataName": "医疗健康数据", "dataRegisterNumber": "A0001113", "listedNumber": "L0001113", "contractNumber": "C0001113", "dataCategory": "公共数据", "dataSecondCategory": "医疗健康", "dataAccessLevel": "受限"}], "total": 3, "current": 1, "size": 5}, "identityData": {"organizationName": "示例数据提供机构", "creditCode": "91330100MA28A1234X", "legalPerson": "张三", "registrationDate": "2023-01-15", "businessScope": "数据服务、技术开发"}, "tokenData": {"tokenId": "TOKEN_001", "tokenName": "智慧城市数据通证", "issueDate": "2023-06-01", "validityPeriod": "2024-06-01", "tokenType": "数据使用权证"}, "directoryData": {"catalogId": "CAT_001", "catalogName": "智慧城市数据目录", "dataFormat": "JSON", "dataSize": "10GB", "updateFrequency": "每日更新", "accessPermission": "授权访问"}, "signData": {"records": [{"dataName": "合同编号001", "contractStartAt": "2023-01-01", "contractEndAt": "2023-12-31", "status": 1, "contractId": "CONTRACT_001"}, {"dataName": "合同编号002", "contractStartAt": "2023-02-01", "contractEndAt": "2024-01-31", "status": 1, "contractId": "CONTRACT_002"}], "total": 2}, "orderData": {"records": [{"dataName": "订单编号001", "contractStartAt": "2023-01-01 09:00:00", "contractEndAt": "2023-01-01 18:00:00", "taskCount": 5, "contractId": "CONTRACT_001"}, {"dataName": "订单编号002", "contractStartAt": "2023-01-02 09:00:00", "contractEndAt": "2023-01-02 18:00:00", "taskCount": 3, "contractId": "CONTRACT_002"}], "total": 2}, "taskData": {"records": [{"taskId": "TASK_001", "taskNumber": "T001", "taskCompletionTime": "2023-01-01 17:30:00", "transactionHash": "0x1234567890abcdef", "taskStatus": 1002}, {"taskId": "TASK_002", "taskNumber": "T002", "taskCompletionTime": "2023-01-02 16:45:00", "transactionHash": "0xabcdef1234567890", "taskStatus": 1001}], "total": 2}, "taskContentData": {"taskId": "TASK_001", "taskName": "数据隐私计算任务", "algorithm": "联邦学习", "inputData": "用户行为数据", "outputResult": "用户画像模型", "executionTime": "2023-01-01 17:30:00"}, "billingData": {"records": [{"contractNumber": "C0001111", "anonymousQueryFee": 100, "privacyIntersectionFee": 200, "privacyCalculationCount": 150, "nodeCost": 450}, {"contractNumber": "C0001112", "anonymousQueryFee": 80, "privacyIntersectionFee": 180, "privacyCalculationCount": 120, "nodeCost": 380}], "total": 2}, "revenueData": {"totalRevenue": 10000, "providerShare": 6000, "platformShare": 2000, "operatorShare": 2000, "distributionDate": "2023-01-31"}, "listedVoucherData": {"voucherId": "LV_001", "voucherType": "登记挂牌凭证", "issueDate": "2023-01-15", "validityPeriod": "长期有效", "certificateUrl": "/images/listed_certificate.png"}, "registerVoucherData": {"voucherId": "RV_001", "voucherType": "注册凭证", "issueDate": "2023-01-10", "validityPeriod": "长期有效", "certificateUrl": "/images/register_certificate.png"}, "deliveryRecordData": {"recordId": "DR_001", "deliveryDate": "2023-01-01", "deliveryStatus": "已完成", "deliveryHash": "0xdelivery123456", "verificationResult": "验证通过"}, "cascaderOptions": [{"value": "1", "label": "公共数据", "children": [{"value": "11", "label": "城市管理"}, {"value": "12", "label": "交通运输"}]}, {"value": "2", "label": "企业数据", "children": [{"value": "21", "label": "金融"}, {"value": "22", "label": "制造业"}]}]}}